// src/common/pipes/json-validate.pipe.ts
import {
  PipeTransform,
  Injectable,
  ArgumentMetadata,
  BadRequestException,
} from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { validateSync, ValidationError } from 'class-validator';

export interface JsonValidateOptions {
  whitelist?: boolean;
  forbidNonWhitelisted?: boolean;
  skipMissingProperties?: boolean;
}

@Injectable()
export class JsonValidatePipe<T extends object>
  implements PipeTransform<string, T>
{
  constructor(
    private readonly dto: new () => T,
    private readonly options: JsonValidateOptions = {
      whitelist: true,
      forbidNonWhitelisted: true,
      skipMissingProperties: true,
    },
  ) {}

  transform(value: any, _metadata: ArgumentMetadata): T {
    if (typeof value !== 'string') {
      const instance = plainToInstance(this.dto, value || {});
      const errors = validateSync(instance, this.options);
      if (errors.length) {
        const messages = errors
          .map((err) => Object.values(err.constraints || {}).join(', '))
          .join('; ');
        throw new BadRequestException(`Validation failed: ${messages}`);
      }
      return instance;
    }

    if (!value) {
      return plainToInstance(this.dto, {});
    }

    let parsed: any;
    try {
      parsed = JSON.parse(value);
    } catch {
      throw new BadRequestException('Invalid JSON payload');
    }

    // 4) transform + validate as before
    const instance = plainToInstance(this.dto, parsed);
    const errors = validateSync(instance, this.options);
    if (errors.length) {
      const messages = errors
        .map((err) => Object.values(err.constraints || {}).join(', '))
        .join('; ');
      throw new BadRequestException(`Validation failed: ${messages}`);
    }

    return instance;
  }
}
