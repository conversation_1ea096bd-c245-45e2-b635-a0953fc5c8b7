// src/common/decorators/isOptionalBoolean.decorator.ts
import { ValidateIf, IsBoolean } from 'class-validator';

/**
 * CUSTOM DECORATOR TO ALLOW NULL, UNDEFINED, OR BOOLEAN VALUES
 * THIS ALLOWS EXPLICIT NULL VALUES TO BE SENT IN API REQUESTS
 */
export function IsOptionalBoolean() {
  return function (target: any, propertyKey: string) {
    ValidateIf((object, value) => value !== null && value !== undefined)(target, propertyKey);
    IsBoolean()(target, propertyKey);
  };
}
