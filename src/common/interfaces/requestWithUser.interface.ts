// src/common/interfaces/requestWithUser.interface.ts
import { Request } from 'express';
import { Doctor } from 'src/doctor/entities/doctor.entity';
import { Patient } from 'src/patient/entities/patient.entity';
import { UserType } from '../enums/userType.enum';
import { DoctorWithUserType } from './doctorWithUserType.interface';
import { PatientWithUserType } from './patientWithUserType.interface';

export interface RequestWithUser extends Request {
  user?: DoctorWithUserType | PatientWithUserType;
}
