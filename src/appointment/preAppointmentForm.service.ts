// src/appointment/preAppointmentForm.service.ts
import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PreAppointmentForm } from './entities/preAppointmentForm.entity';
import { Appointment, AppointmentStatus } from './entities/appointment.entity';
import { CreatePreAppointmentFormDto } from './dtos/create-preAppointmentForm.dto';
import { UpdatePreAppointmentFormDto } from './dtos/update-preAppointmentForm.dto';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';
import { UserType } from 'src/common/enums/userType.enum';
import { EmrService } from 'src/emr/emr.service';

@Injectable()
export class PreAppointmentFormService {
  // CONSTRUCTOR
  constructor(
    @InjectRepository(PreAppointmentForm)
    private readonly preAppointmentFormRepository: Repository<PreAppointmentForm>,
    @InjectRepository(Appointment)
    private readonly appointmentRepository: Repository<Appointment>,

    @Inject(forwardRef(() => EmrService))
    private readonly emrService: EmrService,
  ) {}

  // SERVICES
  async createPreAppointmentForm(
    user: PatientWithUserType,
    createPreAppointmentFormDto: CreatePreAppointmentFormDto,
  ): Promise<PreAppointmentForm> {
    const { appointmentId, ...formData } = createPreAppointmentFormDto;

    // FIND APPOINTMENT WITH RELATIONS
    const appointment = await this.appointmentRepository.findOne({
      where: { id: appointmentId },
      relations: ['patient', 'doctor', 'preAppointmentForm'],
    });

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    // CHECK IF USER IS CONCERNED PATIENT
    if (appointment.patient.id !== user.id) {
      throw new ForbiddenException(
        'You can only create pre-appointment forms for your own appointments',
      );
    }

    // CHECK IF APPOINTMENT STATUS IS UPCOMING
    if (appointment.appointmentStatus !== AppointmentStatus.UPCOMING) {
      throw new BadRequestException(
        'Pre-appointment forms can only be created for upcoming appointments',
      );
    }

    // ENSURE ONLY ONE FORM PER APPOINTMENT - CHECK IF FORM ALREADY EXISTS
    if (appointment.preAppointmentForm) {
      throw new BadRequestException(
        'Pre-appointment form already exists for this appointment. Use update instead.',
      );
    }

    // DOUBLE CHECK - QUERY DIRECTLY TO ENSURE NO FORM EXISTS
    const existingForm = await this.preAppointmentFormRepository.findOne({
      where: { appointment: { id: appointmentId } },
    });

    if (existingForm) {
      throw new BadRequestException(
        'Pre-appointment form already exists for this appointment. Use update instead.',
      );
    }

    // CREATE FORM
    const preAppointmentForm = this.preAppointmentFormRepository.create({
      ...formData,
      appointment,
    });

    return await this.preAppointmentFormRepository.save(preAppointmentForm);
  }
  async updatePreAppointmentForm(
    user: PatientWithUserType,
    updatePreAppointmentFormDto: UpdatePreAppointmentFormDto,
  ): Promise<PreAppointmentForm> {
    const { id, appointmentId, ...updateData } = updatePreAppointmentFormDto;

    // VALIDATE THAT EITHER ID OR APPOINTMENTID IS PROVIDED
    if (!id && !appointmentId) {
      throw new BadRequestException(
        'Either form id or appointmentId must be provided',
      );
    }

    let preAppointmentForm: PreAppointmentForm;

    if (id) {
      // UPDATE BY FORM ID
      preAppointmentForm = await this.preAppointmentFormRepository.findOne({
        where: { id },
        relations: [
          'appointment',
          'appointment.patient',
          'appointment.patient.profilePicture',
          'appointment.doctor',
          'appointment.doctor.profilePicture',
          'appointment.doctor.signature',
        ],
      });

      if (!preAppointmentForm) {
        throw new NotFoundException('Pre-appointment form not found');
      }
    } else {
      // UPDATE BY APPOINTMENT ID
      const appointment = await this.appointmentRepository.findOne({
        where: { id: appointmentId },
        relations: [
          'patient',
          'patient.profilePicture',
          'doctor',
          'doctor.profilePicture',
          'doctor.signature',
          'preAppointmentForm',
        ],
      });

      if (!appointment) {
        throw new NotFoundException('Appointment not found');
      }

      if (!appointment.preAppointmentForm) {
        throw new NotFoundException(
          'Pre-appointment form not found for this appointment',
        );
      }

      preAppointmentForm = appointment.preAppointmentForm;
    }

    // CHECK IF USER IS CONCERNED PATIENT
    if (preAppointmentForm.appointment.patient.id !== user.id) {
      throw new ForbiddenException(
        'You can only update pre-appointment forms for your own appointments',
      );
    }

    // CHECK IF APPOINTMENT STATUS IS UPCOMING
    if (
      preAppointmentForm.appointment.appointmentStatus !==
      AppointmentStatus.UPCOMING
    ) {
      throw new BadRequestException(
        'Pre-appointment forms can only be updated for upcoming appointments',
      );
    }

    // UPDATE FORM
    Object.assign(preAppointmentForm, updateData);

    return await this.preAppointmentFormRepository.save(preAppointmentForm);
  }
  async getPreAppointmentFormById(
    user: DoctorWithUserType | PatientWithUserType,
    id: string,
  ): Promise<PreAppointmentForm> {
    // FIND FORM WITH RELATIONS
    const preAppointmentForm = await this.preAppointmentFormRepository.findOne({
      where: { id },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    if (!preAppointmentForm) {
      throw new NotFoundException('Pre-appointment form not found');
    }

    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN FORMS
      if (preAppointmentForm.appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'You can only view pre-appointment forms for your own appointments',
        );
      }
    }

    // DOCTORS CAN VIEW FORMS IF THEY ARE THE BOOKED DOCTOR OR HAVE SHARED EMR ACCESS
    if (user.userType === UserType.DOCTOR) {
      const isBookedDoctor =
        preAppointmentForm.appointment.doctor.id === user.id;

      if (!isBookedDoctor) {
        const hasSharedEmrAccess =
          await this.emrService.hasDoctorSharedEmrAccess(
            user.id,
            preAppointmentForm.appointment.patient.id,
          );

        if (!hasSharedEmrAccess) {
          throw new ForbiddenException(
            'Not authorized to view this pre-appointment form',
          );
        }
      }
    }

    return preAppointmentForm;
  }
  async getPreAppointmentFormByAppointmentId(
    user: DoctorWithUserType | PatientWithUserType,
    appointmentId: string,
  ): Promise<PreAppointmentForm> {
    // FIND APPOINTMENT FOR AUTHORIZATION CHECK
    const appointment = await this.appointmentRepository.findOne({
      where: { id: appointmentId },
      relations: ['patient', 'doctor'],
    });

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN FORMS
      if (appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'You can only view pre-appointment forms for your own appointments',
        );
      }
    }

    // DOCTORS CAN VIEW FORMS IF THEY ARE THE BOOKED DOCTOR OR HAVE SHARED EMR ACCESS
    if (user.userType === UserType.DOCTOR) {
      const isBookedDoctor = appointment.doctor.id === user.id;

      if (!isBookedDoctor) {
        const hasSharedEmrAccess =
          await this.emrService.hasDoctorSharedEmrAccess(
            user.id,
            appointment.patient.id,
          );

        if (!hasSharedEmrAccess) {
          throw new ForbiddenException(
            'Not authorized to view this pre-appointment form',
          );
        }
      }
    }

    // FETCH PRE-APPOINTMENT FORM WITH FULL RELATIONS
    const preAppointmentForm = await this.preAppointmentFormRepository.findOne({
      where: { appointment: { id: appointmentId } },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    if (!preAppointmentForm) {
      throw new NotFoundException(
        'Pre-appointment form not found for this appointment',
      );
    }

    return preAppointmentForm;
  }
  async getPreAppointmentFormsByPatient(
    user: DoctorWithUserType | PatientWithUserType,
    patientId: string,
  ): Promise<PreAppointmentForm[]> {
    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN FORMS
      if (user.id !== patientId) {
        throw new ForbiddenException(
          'You can only view your own pre-appointment forms',
        );
      }
    }

    // DOCTORS CAN VIEW PATIENT FORMS IF THEY HAVE APPOINTMENTS WITH PATIENT OR SHARED EMR ACCESS
    if (user.userType === UserType.DOCTOR) {
      const appointment = await this.appointmentRepository.findOne({
        where: {
          doctor: { id: user.id },
          patient: { id: patientId },
        },
      });
      const hasAppointments = !!appointment;

      const hasSharedEmrAccess = await this.emrService.hasDoctorSharedEmrAccess(
        user.id,
        patientId,
      );

      if (!hasAppointments && !hasSharedEmrAccess) {
        throw new ForbiddenException(
          "Not authorized to view this patient's pre-appointment forms",
        );
      }
    }

    const preAppointmentForms = await this.preAppointmentFormRepository.find({
      where: { appointment: { patient: { id: patientId } } },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    return preAppointmentForms;
  }
}
