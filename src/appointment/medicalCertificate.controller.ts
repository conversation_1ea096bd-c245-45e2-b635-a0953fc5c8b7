// src/appointment/medicalCertificate.controller.ts
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { MedicalCertificateService } from './medicalCertificate.service';
import { CreateMedicalCertificateDto } from './dtos/create-medicalCertificate.dto';
import { UpdateMedicalCertificateDto } from './dtos/update-medicalCertificate.dto';
import { AuthenticationGuard } from 'src/auth/guards/authentication.guard';
import { AuthorizationGuard } from 'src/auth/guards/authorization.guard';
import { UserTypes } from 'src/common/decorators/userType.decorator';
import { UserType } from 'src/common/enums/userType.enum';
import { RequestWithUser } from 'src/common/interfaces/requestWithUser.interface';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';

@Controller('api/medicalCertificate')
export class MedicalCertificateController {
  // CONSTRUCTOR
  constructor(private readonly medicalCertificateService: MedicalCertificateService) {}

  // CONTROLLERS
  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Post('create')
  async createMedicalCertificate(
    @Body() createMedicalCertificateDto: CreateMedicalCertificateDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.medicalCertificateService.createMedicalCertificate(
      user,
      createMedicalCertificateDto,
    );
    return { message: 'Medical certificate created successfully', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Put('update')
  async updateMedicalCertificate(
    @Body() updateMedicalCertificateDto: UpdateMedicalCertificateDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.medicalCertificateService.updateMedicalCertificate(
      user,
      updateMedicalCertificateDto,
    );
    return { message: 'Medical certificate updated successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getById/:id')
  async getMedicalCertificateById(
    @Param('id') id: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.medicalCertificateService.getMedicalCertificateById(user, id);
    return { message: 'Medical certificate fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getByAppointmentId/:appointmentId')
  async getMedicalCertificateByAppointmentId(
    @Param('appointmentId') appointmentId: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.medicalCertificateService.getMedicalCertificateByAppointmentId(
      user,
      appointmentId,
    );
    return { message: 'Medical certificate fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getByPatientId/:patientId')
  async getMedicalCertificatesByPatient(
    @Param('patientId') patientId: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.medicalCertificateService.getMedicalCertificatesByPatient(
      user,
      patientId,
    );
    return { message: 'Medical certificates fetched successfully', data };
  }
}
