// src/appointment/soapForm.service.ts
import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SoapForm } from './entities/soapForm.entity';
import { Appointment, AppointmentStatus } from './entities/appointment.entity';
import { CreateSoapFormDto } from './dtos/create-soapForm.dto';
import { UpdateSoapFormDto } from './dtos/update-soapForm.dto';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';
import { UserType } from 'src/common/enums/userType.enum';
import { EmrService } from 'src/emr/emr.service';

@Injectable()
export class SoapFormService {
  // CONSTRUCTOR
  constructor(
    @InjectRepository(SoapForm)
    private readonly soapFormRepository: Repository<SoapForm>,
    @InjectRepository(Appointment)
    private readonly appointmentRepository: Repository<Appointment>,
    @Inject(forwardRef(() => EmrService))
    private readonly emrService: EmrService,
  ) {}

  // SERVICES
  async createSoapForm(
    user: DoctorWithUserType,
    createSoapFormDto: CreateSoapFormDto,
  ): Promise<SoapForm> {
    const { appointmentId, ...formData } = createSoapFormDto;

    // FIND APPOINTMENT WITH RELATIONS
    const appointment = await this.appointmentRepository.findOne({
      where: { id: appointmentId },
      relations: [
        'patient',
        'patient.profilePicture',
        'doctor',
        'doctor.profilePicture',
        'doctor.signature',
        'soapForm',
      ],
    });

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    // CHECK IF USER IS THE BOOKED DOCTOR
    if (appointment.doctor.id !== user.id) {
      throw new ForbiddenException(
        'Only the booked doctor can create SOAP forms',
      );
    }

    // CHECK IF APPOINTMENT STATUS ALLOWS FORM CREATION
    if (appointment.appointmentStatus === AppointmentStatus.CANCELLED) {
      throw new BadRequestException(
        'Cannot create SOAP form for cancelled appointment',
      );
    }

    // CHECK IF FORM ALREADY EXISTS
    if (appointment.soapForm) {
      throw new BadRequestException(
        'SOAP form already exists for this appointment',
      );
    }

    // CREATE FORM
    const soapForm = this.soapFormRepository.create({
      ...formData,
      appointment,
    });

    return await this.soapFormRepository.save(soapForm);
  }

  async updateSoapForm(
    user: DoctorWithUserType,
    updateSoapFormDto: UpdateSoapFormDto,
  ): Promise<SoapForm> {
    const { id, appointmentId, ...updateData } = updateSoapFormDto;

    // VALIDATE THAT EITHER ID OR APPOINTMENTID IS PROVIDED
    if (!id && !appointmentId) {
      throw new BadRequestException(
        'Either form id or appointmentId must be provided',
      );
    }

    let soapForm: SoapForm;

    if (id) {
      // FIND BY FORM ID
      soapForm = await this.soapFormRepository.findOne({
        where: { id },
        relations: [
          'appointment',
          'appointment.patient',
          'appointment.patient.profilePicture',
          'appointment.doctor',
          'appointment.doctor.profilePicture',
          'appointment.doctor.signature',
        ],
      });
    } else {
      // FIND BY APPOINTMENT ID
      soapForm = await this.soapFormRepository.findOne({
        where: { appointment: { id: appointmentId } },
        relations: [
          'appointment',
          'appointment.patient',
          'appointment.patient.profilePicture',
          'appointment.doctor',
          'appointment.doctor.profilePicture',
          'appointment.doctor.signature',
        ],
      });
    }

    if (!soapForm) {
      throw new NotFoundException('SOAP form not found');
    }

    // CHECK IF USER IS THE BOOKED DOCTOR
    if (soapForm.appointment.doctor.id !== user.id) {
      throw new ForbiddenException(
        'Only the booked doctor can edit SOAP forms',
      );
    }

    // CHECK IF APPOINTMENT STATUS ALLOWS FORM EDITING
    if (
      soapForm.appointment.appointmentStatus === AppointmentStatus.CANCELLED
    ) {
      throw new BadRequestException(
        'Cannot edit SOAP form for cancelled appointment',
      );
    }

    // UPDATE FORM
    Object.assign(soapForm, updateData);
    return await this.soapFormRepository.save(soapForm);
  }

  async getSoapFormById(
    user: DoctorWithUserType | PatientWithUserType,
    id: string,
  ): Promise<SoapForm> {
    // FIND FORM WITH RELATIONS
    const soapForm = await this.soapFormRepository.findOne({
      where: { id },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    if (!soapForm) {
      throw new NotFoundException('SOAP form not found');
    }

    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN FORMS
      if (soapForm.appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'You can only view SOAP forms for your own appointments',
        );
      }
    } else if (user.userType === UserType.DOCTOR) {
      // DOCTOR CAN VIEW IF THEY ARE THE BOOKED DOCTOR OR HAVE SHARED EMR ACCESS
      const isBookedDoctor = soapForm.appointment.doctor.id === user.id;
      const hasSharedAccess = await this.emrService.hasDoctorSharedEmrAccess(
        user.id,
        soapForm.appointment.patient.id,
      );

      if (!isBookedDoctor && !hasSharedAccess) {
        throw new ForbiddenException(
          'You can only view SOAP forms for your appointments or patients with shared EMR access',
        );
      }
    }

    return soapForm;
  }

  async getSoapFormByAppointmentId(
    user: DoctorWithUserType | PatientWithUserType,
    appointmentId: string,
  ): Promise<SoapForm> {
    // FIND APPOINTMENT FOR AUTHORIZATION CHECK
    const appointment = await this.appointmentRepository.findOne({
      where: { id: appointmentId },
      relations: ['patient', 'doctor'],
    });

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN FORMS
      if (appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'You can only view SOAP forms for your own appointments',
        );
      }
    } else if (user.userType === UserType.DOCTOR) {
      // DOCTOR CAN VIEW IF THEY ARE THE BOOKED DOCTOR OR HAVE SHARED EMR ACCESS
      const isBookedDoctor = appointment.doctor.id === user.id;
      const hasSharedAccess = await this.emrService.hasDoctorSharedEmrAccess(
        user.id,
        appointment.patient.id,
      );

      if (!isBookedDoctor && !hasSharedAccess) {
        throw new ForbiddenException(
          'You can only view SOAP forms for your appointments or patients with shared EMR access',
        );
      }
    }

    // FETCH SOAP FORM WITH FULL RELATIONS
    const soapForm = await this.soapFormRepository.findOne({
      where: { appointment: { id: appointmentId } },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    if (!soapForm) {
      throw new NotFoundException('SOAP form not found for this appointment');
    }

    return soapForm;
  }
}
