// src/appointment/preAppointmentForm.controller.ts
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { PreAppointmentFormService } from './preAppointmentForm.service';
import { CreatePreAppointmentFormDto } from './dtos/create-preAppointmentForm.dto';
import { UpdatePreAppointmentFormDto } from './dtos/update-preAppointmentForm.dto';
import { AuthenticationGuard } from 'src/auth/guards/authentication.guard';
import { AuthorizationGuard } from 'src/auth/guards/authorization.guard';
import { UserTypes } from 'src/common/decorators/userType.decorator';
import { UserType } from 'src/common/enums/userType.enum';
import { RequestWithUser } from 'src/common/interfaces/requestWithUser.interface';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';

@Controller('api/preAppointmentForm')
export class PreAppointmentFormController {
  // CONSTRUCTOR
  constructor(
    private readonly preAppointmentFormService: PreAppointmentFormService,
  ) {}

  // CONTROLLERS
  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.PATIENT)
  @Post('create')
  async createPreAppointmentForm(
    @Body() createPreAppointmentFormDto: CreatePreAppointmentFormDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as PatientWithUserType;
    const data = await this.preAppointmentFormService.createPreAppointmentForm(
      user,
      createPreAppointmentFormDto,
    );
    return { message: 'Pre-appointment form created successfully', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.PATIENT)
  @Put('update')
  async updatePreAppointmentForm(
    @Body() updatePreAppointmentFormDto: UpdatePreAppointmentFormDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as PatientWithUserType;
    const data = await this.preAppointmentFormService.updatePreAppointmentForm(
      user,
      updatePreAppointmentFormDto,
    );
    return { message: 'Pre-appointment form updated successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getById/:id')
  async getPreAppointmentFormById(
    @Param('id') id: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.preAppointmentFormService.getPreAppointmentFormById(
      user,
      id,
    );
    return { message: 'Pre-appointment form fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getByAppointmentId/:appointmentId')
  async getPreAppointmentFormByAppointmentId(
    @Param('appointmentId') appointmentId: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data =
      await this.preAppointmentFormService.getPreAppointmentFormByAppointmentId(
        user,
        appointmentId,
      );
    return { message: 'Pre-appointment form fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getByPatientId/:patientId')
  async getPreAppointmentFormsByPatient(
    @Param('patientId') patientId: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data =
      await this.preAppointmentFormService.getPreAppointmentFormsByPatient(
        user,
        patientId,
      );
    return { message: 'Pre-appointment forms fetched successfully', data };
  }
}
