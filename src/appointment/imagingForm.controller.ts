// src/appointment/imagingForm.controller.ts
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ImagingFormService } from './imagingForm.service';
import { CreateImagingFormDto } from './dtos/create-imagingForm.dto';
import { UpdateImagingFormDto } from './dtos/update-imagingForm.dto';
import { AuthenticationGuard } from 'src/auth/guards/authentication.guard';
import { AuthorizationGuard } from 'src/auth/guards/authorization.guard';
import { UserTypes } from 'src/common/decorators/userType.decorator';
import { UserType } from 'src/common/enums/userType.enum';
import { RequestWithUser } from 'src/common/interfaces/requestWithUser.interface';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';

@Controller('api/imagingForm')
export class ImagingFormController {
  // CONSTRUCTOR
  constructor(private readonly imagingFormService: ImagingFormService) {}

  // CONTROLLERS
  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Post('create')
  async createImagingForm(
    @Body() createImagingFormDto: CreateImagingFormDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.imagingFormService.createImagingForm(
      user,
      createImagingFormDto,
    );
    return { message: 'Imaging form created successfully', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Put('update')
  async updateImagingForm(
    @Body() updateImagingFormDto: UpdateImagingFormDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.imagingFormService.updateImagingForm(
      user,
      updateImagingFormDto,
    );
    return { message: 'Imaging form updated successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getById/:id')
  async getImagingFormById(
    @Param('id') id: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.imagingFormService.getImagingFormById(user, id);
    return { message: 'Imaging form fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getByAppointmentId/:appointmentId')
  async getImagingFormByAppointmentId(
    @Param('appointmentId') appointmentId: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.imagingFormService.getImagingFormByAppointmentId(
      user,
      appointmentId,
    );
    return { message: 'Imaging form fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getByPatientId/:patientId')
  async getImagingFormsByPatient(
    @Param('patientId') patientId: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.imagingFormService.getImagingFormsByPatient(
      user,
      patientId,
    );
    return { message: 'Imaging forms fetched successfully', data };
  }
}
