// src/appointment/rosForm.controller.ts
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { RosFormService } from './rosForm.service';
import { CreateRosFormDto } from './dtos/create-rosForm.dto';
import { UpdateRosFormDto } from './dtos/update-rosForm.dto';
import { AuthenticationGuard } from 'src/auth/guards/authentication.guard';
import { AuthorizationGuard } from 'src/auth/guards/authorization.guard';
import { UserTypes } from 'src/common/decorators/userType.decorator';
import { UserType } from 'src/common/enums/userType.enum';
import { RequestWithUser } from 'src/common/interfaces/requestWithUser.interface';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';

@Controller('api/rosForm')
export class RosFormController {
  // CONSTRUCTOR
  constructor(private readonly rosFormService: RosFormService) {}

  // CONTROLLERS
  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Post('create')
  async createRosForm(
    @Body() createRosFormDto: CreateRosFormDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.rosFormService.createRosForm(
      user,
      createRosFormDto,
    );
    return { message: 'ROS form created successfully', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Put('update')
  async updateRosForm(
    @Body() updateRosFormDto: UpdateRosFormDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.rosFormService.updateRosForm(
      user,
      updateRosFormDto,
    );
    return { message: 'ROS form updated successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getById/:id')
  async getRosFormById(
    @Param('id') id: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.rosFormService.getRosFormById(user, id);
    return { message: 'ROS form fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getByAppointmentId/:appointmentId')
  async getRosFormByAppointmentId(
    @Param('appointmentId') appointmentId: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.rosFormService.getRosFormByAppointmentId(
      user,
      appointmentId,
    );
    return { message: 'ROS form fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getByPatientId/:patientId')
  async getRosFormsByPatient(
    @Param('patientId') patientId: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.rosFormService.getRosFormsByPatient(
      user,
      patientId,
    );
    return { message: 'ROS forms fetched successfully', data };
  }
}
