// src/appointment/appointment.controller.ts
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AppointmentService } from './appointment.service';
import { CreateAppointmentDto } from './dtos/create-appointment.dto';
import { CancelAppointmentDto } from './dtos/cancel-appointment.dto';
import { FetchPatientAppointmentsDto } from './dtos/fetch-patient-appointments.dto';
import { FetchDoctorAppointmentsDto } from './dtos/fetch-doctor-appointments.dto';
import { AuthenticationGuard } from 'src/auth/guards/authentication.guard';
import { AuthorizationGuard } from 'src/auth/guards/authorization.guard';
import { UserTypes } from 'src/common/decorators/userType.decorator';
import { UserType } from 'src/common/enums/userType.enum';
import { RequestWithUser } from 'src/common/interfaces/requestWithUser.interface';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';

@Controller('api/appointment')
export class AppointmentController {
  // CONSTRUCTOR
  constructor(private readonly appointmentService: AppointmentService) {}

  // CONTROLLERS
  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.PATIENT)
  @Post('book')
  async bookAppointment(
    @Body() createAppointmentDto: CreateAppointmentDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as PatientWithUserType;
    const data = await this.appointmentService.bookAppointment(
      user,
      createAppointmentDto,
    );
    return { message: 'Appointment booked successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Put('cancel')
  async cancelAppointment(
    @Body() cancelAppointmentDto: CancelAppointmentDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.appointmentService.cancelAppointment(
      user,
      cancelAppointmentDto,
    );
    return { message: 'Appointment cancelled successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getById/:id')
  async getAppointmentById(
    @Param('id') id: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.appointmentService.getAppointmentById(user, id);
    return { message: 'Appointment fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getByPatientId/:patientId')
  async getAppointmentsOfPatient(
    @Param('patientId') patientId: string,
    @Query() fetchPatientAppointmentsDto: FetchPatientAppointmentsDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.appointmentService.getAppointmentsOfPatient(
      user,
      patientId,
      fetchPatientAppointmentsDto,
    );
    return { message: 'Appointments fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Get('getByDoctorId/:doctorId?')
  async getAppointmentsOfDoctor(
    @Param('doctorId') doctorId: string | undefined,
    @Query() fetchDoctorAppointmentsDto: FetchDoctorAppointmentsDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.appointmentService.getAppointmentsOfDoctor(
      user,
      doctorId,
      fetchDoctorAppointmentsDto,
    );
    return { message: 'Appointments fetched successfully', data };
  }
}
