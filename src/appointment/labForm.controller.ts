// src/appointment/labForm.controller.ts
import {
  Controller,
  Post,
  Put,
  Get,
  Body,
  Param,
  UseGuards,
  Req,
} from '@nestjs/common';
import { LabFormService } from './labForm.service';
import { CreateLabFormDto } from './dtos/create-labForm.dto';
import { UpdateLabFormDto } from './dtos/update-labForm.dto';
import { AuthenticationGuard } from 'src/auth/guards/authentication.guard';
import { AuthorizationGuard } from 'src/auth/guards/authorization.guard';
import { UserTypes } from 'src/common/decorators/userType.decorator';
import { UserType } from 'src/common/enums/userType.enum';
import { RequestWithUser } from 'src/common/interfaces/requestWithUser.interface';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';

@Controller('api/labForm')
export class LabFormController {
  // CONSTRUCTOR
  constructor(private readonly labFormService: LabFormService) {}

  // CONTROLLERS
  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Post('create')
  async createLabForm(
    @Body() createLabFormDto: CreateLabFormDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.labFormService.createLabForm(
      user,
      createLabFormDto,
    );
    return { message: 'Lab form created successfully', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Put('update')
  async updateLabForm(
    @Body() updateLabFormDto: UpdateLabFormDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.labFormService.updateLabForm(
      user,
      updateLabFormDto,
    );
    return { message: 'Lab form updated successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getById/:id')
  async getLabFormById(@Param('id') id: string, @Req() req: RequestWithUser) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.labFormService.getLabFormById(user, id);
    return { message: 'Lab form fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getByAppointmentId/:appointmentId')
  async getLabFormByAppointmentId(
    @Param('appointmentId') appointmentId: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.labFormService.getLabFormByAppointmentId(
      user,
      appointmentId,
    );
    return { message: 'Lab form fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getByPatientId/:patientId')
  async getLabFormsByPatientId(
    @Param('patientId') patientId: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.labFormService.getLabFormsByPatientId(
      user,
      patientId,
    );
    return { message: 'Lab forms fetched successfully', data };
  }
}
