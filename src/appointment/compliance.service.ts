// src/appointment/compliance.service.ts
import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Compliance, ComplianceType } from './entities/compliance.entity';
import { Appointment, AppointmentStatus } from './entities/appointment.entity';
import { CreateComplianceDto } from './dtos/create-compliance.dto';
import { UpdateComplianceDto } from './dtos/update-compliance.dto';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';
import { UserType } from 'src/common/enums/userType.enum';
import { EmrService } from 'src/emr/emr.service';

@Injectable()
export class ComplianceService {
  // CONSTRUCTOR
  constructor(
    @InjectRepository(Compliance)
    private readonly complianceRepository: Repository<Compliance>,
    @InjectRepository(Appointment)
    private readonly appointmentRepository: Repository<Appointment>,
    @Inject(forwardRef(() => EmrService))
    private readonly emrService: EmrService,
  ) {}

  // SERVICES
  async createCompliance(
    user: DoctorWithUserType | PatientWithUserType,
    createComplianceDto: CreateComplianceDto,
  ): Promise<Compliance> {
    const { appointmentId, type, log } = createComplianceDto;

    // FIND APPOINTMENT WITH RELATIONS
    const appointment = await this.appointmentRepository.findOne({
      where: { id: appointmentId },
      relations: [
        'patient',
        'patient.profilePicture',
        'doctor',
        'doctor.profilePicture',
        'doctor.signature',
      ],
    });

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    // VALIDATE USER AUTHORIZATION BASED ON COMPLIANCE TYPE
    if (type === ComplianceType.DOCTOR) {
      // ONLY BOOKED DOCTOR CAN CREATE DOCTOR COMPLIANCE
      if (user.userType !== UserType.DOCTOR) {
        throw new ForbiddenException(
          'Only doctors can create doctor compliance',
        );
      }
      if (appointment.doctor.id !== user.id) {
        throw new ForbiddenException(
          'Only the booked doctor can create doctor compliance',
        );
      }
    } else if (type === ComplianceType.PATIENT) {
      // ONLY APPOINTMENT PATIENT CAN CREATE PATIENT COMPLIANCE
      if (user.userType !== UserType.PATIENT) {
        throw new ForbiddenException(
          'Only patients can create patient compliance',
        );
      }
      if (appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'Only the appointment patient can create patient compliance',
        );
      }
    }

    // CHECK IF APPOINTMENT STATUS IS UPCOMING OR COMPLETED
    if (
      appointment.appointmentStatus !== AppointmentStatus.UPCOMING &&
      appointment.appointmentStatus !== AppointmentStatus.COMPLETED
    ) {
      throw new BadRequestException(
        'Compliance can only be created for upcoming or completed appointments',
      );
    }

    // CREATE COMPLIANCE
    const compliance = this.complianceRepository.create({
      type,
      log,
      appointment,
    });

    return await this.complianceRepository.save(compliance);
  }

  async updateCompliance(
    user: DoctorWithUserType | PatientWithUserType,
    updateComplianceDto: UpdateComplianceDto,
  ): Promise<Compliance> {
    const { id, ...updateData } = updateComplianceDto;

    // FIND COMPLIANCE BY ID
    const compliance = await this.complianceRepository.findOne({
      where: { id },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    if (!compliance) {
      throw new NotFoundException('Compliance not found');
    }

    // VALIDATE USER AUTHORIZATION BASED ON COMPLIANCE TYPE
    if (compliance.type === ComplianceType.DOCTOR) {
      // ONLY BOOKED DOCTOR CAN EDIT DOCTOR COMPLIANCE
      if (user.userType !== UserType.DOCTOR) {
        throw new ForbiddenException('Only doctors can edit doctor compliance');
      }
      if (compliance.appointment.doctor.id !== user.id) {
        throw new ForbiddenException(
          'Only the booked doctor can edit doctor compliance',
        );
      }
    } else if (compliance.type === ComplianceType.PATIENT) {
      // ONLY APPOINTMENT PATIENT CAN EDIT PATIENT COMPLIANCE
      if (user.userType !== UserType.PATIENT) {
        throw new ForbiddenException(
          'Only patients can edit patient compliance',
        );
      }
      if (compliance.appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'Only the appointment patient can edit patient compliance',
        );
      }
    }

    // CHECK IF APPOINTMENT STATUS IS UPCOMING OR COMPLETED
    if (
      compliance.appointment.appointmentStatus !== AppointmentStatus.UPCOMING &&
      compliance.appointment.appointmentStatus !== AppointmentStatus.COMPLETED
    ) {
      throw new BadRequestException(
        'Compliance can only be edited for upcoming or completed appointments',
      );
    }

    // UPDATE COMPLIANCE
    Object.assign(compliance, updateData);
    return await this.complianceRepository.save(compliance);
  }

  async deleteCompliance(
    user: DoctorWithUserType | PatientWithUserType,
    id: string,
  ): Promise<void> {
    // FIND COMPLIANCE WITH RELATIONS
    const compliance = await this.complianceRepository.findOne({
      where: { id },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    if (!compliance) {
      throw new NotFoundException('Compliance not found');
    }

    // VALIDATE USER AUTHORIZATION BASED ON COMPLIANCE TYPE
    if (compliance.type === ComplianceType.DOCTOR) {
      // ONLY BOOKED DOCTOR CAN DELETE DOCTOR COMPLIANCE
      if (user.userType !== UserType.DOCTOR) {
        throw new ForbiddenException(
          'Only doctors can delete doctor compliance',
        );
      }
      if (compliance.appointment.doctor.id !== user.id) {
        throw new ForbiddenException(
          'Only the booked doctor can delete doctor compliance',
        );
      }
    } else if (compliance.type === ComplianceType.PATIENT) {
      // ONLY APPOINTMENT PATIENT CAN DELETE PATIENT COMPLIANCE
      if (user.userType !== UserType.PATIENT) {
        throw new ForbiddenException(
          'Only patients can delete patient compliance',
        );
      }
      if (compliance.appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'Only the appointment patient can delete patient compliance',
        );
      }
    }

    // DELETE COMPLIANCE
    await this.complianceRepository.remove(compliance);
  }

  async getComplianceById(
    user: DoctorWithUserType | PatientWithUserType,
    id: string,
  ): Promise<Compliance> {
    // FIND COMPLIANCE BY ID
    const compliance = await this.complianceRepository.findOne({
      where: { id },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    if (!compliance) {
      throw new NotFoundException('Compliance not found');
    }

    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN APPOINTMENT COMPLIANCES
      if (compliance.appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'You can only view compliance for your own appointments',
        );
      }
    } else if (user.userType === UserType.DOCTOR) {
      // DOCTOR CAN VIEW IF THEY ARE THE BOOKED DOCTOR OR HAVE SHARED EMR ACCESS
      const isBookedDoctor = compliance.appointment.doctor.id === user.id;
      const hasSharedAccess = await this.emrService.hasDoctorSharedEmrAccess(
        user.id,
        compliance.appointment.patient.id,
      );

      if (!isBookedDoctor && !hasSharedAccess) {
        throw new ForbiddenException(
          'You can only view compliance for your appointments or patients with shared EMR access',
        );
      }
    }

    return compliance;
  }

  async getCompliancesByAppointmentId(
    user: DoctorWithUserType | PatientWithUserType,
    appointmentId: string,
  ): Promise<{ compliances: Compliance[] }> {
    // FIND APPOINTMENT WITH RELATIONS
    const appointment = await this.appointmentRepository.findOne({
      where: { id: appointmentId },
      relations: [
        'patient',
        'patient.profilePicture',
        'doctor',
        'doctor.profilePicture',
        'doctor.signature',
      ],
    });

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN APPOINTMENT COMPLIANCES
      if (appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'You can only view compliance for your own appointments',
        );
      }
    } else if (user.userType === UserType.DOCTOR) {
      // DOCTOR CAN VIEW IF THEY ARE THE BOOKED DOCTOR OR HAVE SHARED EMR ACCESS
      const isBookedDoctor = appointment.doctor.id === user.id;
      const hasSharedAccess = await this.emrService.hasDoctorSharedEmrAccess(
        user.id,
        appointment.patient.id,
      );

      if (!isBookedDoctor && !hasSharedAccess) {
        throw new ForbiddenException(
          'You can only view compliance for your appointments or patients with shared EMR access',
        );
      }
    }

    // GET ALL COMPLIANCES FOR THE APPOINTMENT
    const compliances = await this.complianceRepository.find({
      where: { appointment: { id: appointmentId } },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
      order: { createdAt: 'DESC' },
    });

    return { compliances };
  }

  async getDoctorCompliancesByAppointmentId(
    user: DoctorWithUserType | PatientWithUserType,
    appointmentId: string,
  ): Promise<{ compliances: Compliance[] }> {
    // FIND APPOINTMENT WITH RELATIONS
    const appointment = await this.appointmentRepository.findOne({
      where: { id: appointmentId },
      relations: [
        'patient',
        'patient.profilePicture',
        'doctor',
        'doctor.profilePicture',
        'doctor.signature',
      ],
    });

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN APPOINTMENT COMPLIANCES
      if (appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'You can only view compliance for your own appointments',
        );
      }
    } else if (user.userType === UserType.DOCTOR) {
      // DOCTOR CAN VIEW IF THEY ARE THE BOOKED DOCTOR OR HAVE SHARED EMR ACCESS
      const isBookedDoctor = appointment.doctor.id === user.id;
      const hasSharedAccess = await this.emrService.hasDoctorSharedEmrAccess(
        user.id,
        appointment.patient.id,
      );

      if (!isBookedDoctor && !hasSharedAccess) {
        throw new ForbiddenException(
          'You can only view compliance for your appointments or patients with shared EMR access',
        );
      }
    }

    // GET DOCTOR COMPLIANCES FOR THE APPOINTMENT
    const compliances = await this.complianceRepository.find({
      where: {
        appointment: { id: appointmentId },
        type: ComplianceType.DOCTOR,
      },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
      order: { createdAt: 'DESC' },
    });

    return { compliances };
  }

  async getPatientCompliancesByAppointmentId(
    user: DoctorWithUserType | PatientWithUserType,
    appointmentId: string,
  ): Promise<{ compliances: Compliance[] }> {
    // FIND APPOINTMENT WITH RELATIONS
    const appointment = await this.appointmentRepository.findOne({
      where: { id: appointmentId },
      relations: [
        'patient',
        'patient.profilePicture',
        'doctor',
        'doctor.profilePicture',
        'doctor.signature',
      ],
    });

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN APPOINTMENT COMPLIANCES
      if (appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'You can only view compliance for your own appointments',
        );
      }
    } else if (user.userType === UserType.DOCTOR) {
      // DOCTOR CAN VIEW IF THEY ARE THE BOOKED DOCTOR OR HAVE SHARED EMR ACCESS
      const isBookedDoctor = appointment.doctor.id === user.id;
      const hasSharedAccess = await this.emrService.hasDoctorSharedEmrAccess(
        user.id,
        appointment.patient.id,
      );

      if (!isBookedDoctor && !hasSharedAccess) {
        throw new ForbiddenException(
          'You can only view compliance for your appointments or patients with shared EMR access',
        );
      }
    }

    // GET PATIENT COMPLIANCES FOR THE APPOINTMENT
    const compliances = await this.complianceRepository.find({
      where: {
        appointment: { id: appointmentId },
        type: ComplianceType.PATIENT,
      },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
      order: { createdAt: 'DESC' },
    });

    return { compliances };
  }
}
