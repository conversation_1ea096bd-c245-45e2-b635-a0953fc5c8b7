// src/appointment/rosForm.service.ts
import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RosForm } from './entities/rosForm.entity';
import { Appointment, AppointmentStatus } from './entities/appointment.entity';
import { CreateRosFormDto } from './dtos/create-rosForm.dto';
import { UpdateRosFormDto } from './dtos/update-rosForm.dto';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';
import { UserType } from 'src/common/enums/userType.enum';
import { EmrService } from 'src/emr/emr.service';

@Injectable()
export class RosFormService {
  // CONSTRUCTOR
  constructor(
    @InjectRepository(RosForm)
    private readonly rosFormRepository: Repository<RosForm>,
    @InjectRepository(Appointment)
    private readonly appointmentRepository: Repository<Appointment>,
    @Inject(forwardRef(() => EmrService))
    private readonly emrService: EmrService,
  ) {}

  // SERVICES
  async createRosForm(
    user: DoctorWithUserType,
    createRosFormDto: CreateRosFormDto,
  ): Promise<RosForm> {
    const { appointmentId, ...formData } = createRosFormDto;

    // FIND APPOINTMENT WITH RELATIONS
    const appointment = await this.appointmentRepository.findOne({
      where: { id: appointmentId },
      relations: [
        'patient',
        'patient.profilePicture',
        'doctor',
        'doctor.profilePicture',
        'doctor.signature',
        'rosForm',
      ],
    });

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    // CHECK IF USER IS THE BOOKED DOCTOR
    if (appointment.doctor.id !== user.id) {
      throw new ForbiddenException(
        'Only the booked doctor can create ROS forms',
      );
    }

    // CHECK IF APPOINTMENT STATUS IS UPCOMING OR COMPLETED
    if (
      appointment.appointmentStatus !== AppointmentStatus.UPCOMING &&
      appointment.appointmentStatus !== AppointmentStatus.COMPLETED
    ) {
      throw new BadRequestException(
        'ROS forms can only be created for upcoming or completed appointments',
      );
    }

    // CHECK IF ROS FORM ALREADY EXISTS
    if (appointment.rosForm) {
      throw new BadRequestException(
        'ROS form already exists for this appointment',
      );
    }

    // CREATE FORM
    const rosForm = this.rosFormRepository.create({
      ...formData,
      appointment,
    });

    return await this.rosFormRepository.save(rosForm);
  }
  async updateRosForm(
    user: DoctorWithUserType,
    updateRosFormDto: UpdateRosFormDto,
  ): Promise<RosForm> {
    const { id, appointmentId, ...updateData } = updateRosFormDto;

    // VALIDATE THAT EITHER ID OR APPOINTMENTID IS PROVIDED
    if (!id && !appointmentId) {
      throw new BadRequestException(
        'Either form id or appointmentId must be provided',
      );
    }

    let rosForm: RosForm;

    if (id) {
      // FIND BY FORM ID
      rosForm = await this.rosFormRepository.findOne({
        where: { id },
        relations: [
          'appointment',
          'appointment.patient',
          'appointment.patient.profilePicture',
          'appointment.doctor',
          'appointment.doctor.profilePicture',
          'appointment.doctor.signature',
        ],
      });
    } else {
      // FIND BY APPOINTMENT ID
      rosForm = await this.rosFormRepository.findOne({
        where: { appointment: { id: appointmentId } },
        relations: [
          'appointment',
          'appointment.patient',
          'appointment.patient.profilePicture',
          'appointment.doctor',
          'appointment.doctor.profilePicture',
          'appointment.doctor.signature',
        ],
      });
    }

    if (!rosForm) {
      throw new NotFoundException('ROS form not found');
    }

    // CHECK IF USER IS THE BOOKED DOCTOR
    if (rosForm.appointment.doctor.id !== user.id) {
      throw new ForbiddenException('Only the booked doctor can edit ROS forms');
    }

    // CHECK IF APPOINTMENT STATUS IS UPCOMING OR COMPLETED
    if (
      rosForm.appointment.appointmentStatus !== AppointmentStatus.UPCOMING &&
      rosForm.appointment.appointmentStatus !== AppointmentStatus.COMPLETED
    ) {
      throw new BadRequestException(
        'ROS forms can only be edited for upcoming or completed appointments',
      );
    }

    // UPDATE FORM
    Object.assign(rosForm, updateData);
    return await this.rosFormRepository.save(rosForm);
  }
  async getRosFormById(
    user: DoctorWithUserType | PatientWithUserType,
    id: string,
  ): Promise<RosForm> {
    // FIND FORM WITH RELATIONS
    const rosForm = await this.rosFormRepository.findOne({
      where: { id },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    if (!rosForm) {
      throw new NotFoundException('ROS form not found');
    }

    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN FORMS
      if (rosForm.appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'You can only view ROS forms for your own appointments',
        );
      }
    }

    if (user.userType === UserType.DOCTOR) {
      // DOCTORS CAN VIEW FORMS IF THEY ARE THE BOOKED DOCTOR OR HAVE SHARED EMR ACCESS
      const isBookedDoctor = rosForm.appointment.doctor.id === user.id;
      const hasSharedEmrAccess = await this.emrService.hasDoctorSharedEmrAccess(
        user.id,
        rosForm.appointment.patient.id,
      );

      if (!isBookedDoctor && !hasSharedEmrAccess) {
        throw new ForbiddenException('Not authorized to view this ROS form');
      }
    }

    return rosForm;
  }
  async getRosFormByAppointmentId(
    user: DoctorWithUserType | PatientWithUserType,
    appointmentId: string,
  ): Promise<RosForm> {
    // FIND APPOINTMENT FOR AUTHORIZATION CHECK
    const appointment = await this.appointmentRepository.findOne({
      where: { id: appointmentId },
      relations: ['patient', 'doctor'],
    });

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN FORMS
      if (appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'You can only view ROS forms for your own appointments',
        );
      }
    }

    if (user.userType === UserType.DOCTOR) {
      // DOCTORS CAN VIEW FORMS IF THEY ARE THE BOOKED DOCTOR OR HAVE SHARED EMR ACCESS
      const isBookedDoctor = appointment.doctor.id === user.id;
      const hasSharedEmrAccess = await this.emrService.hasDoctorSharedEmrAccess(
        user.id,
        appointment.patient.id,
      );

      if (!isBookedDoctor && !hasSharedEmrAccess) {
        throw new ForbiddenException('Not authorized to view this ROS form');
      }
    }

    // FETCH ROS FORM WITH FULL RELATIONS
    const rosForm = await this.rosFormRepository.findOne({
      where: { appointment: { id: appointmentId } },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    if (!rosForm) {
      throw new NotFoundException('ROS form not found for this appointment');
    }

    return rosForm;
  }
  async getRosFormsByPatient(
    user: DoctorWithUserType | PatientWithUserType,
    patientId: string,
  ): Promise<RosForm[]> {
    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN FORMS
      if (user.id !== patientId) {
        throw new ForbiddenException('You can only view your own ROS forms');
      }
    }

    // DOCTORS CAN VIEW PATIENT FORMS IF THEY HAVE APPOINTMENTS WITH PATIENT OR SHARED EMR ACCESS
    if (user.userType === UserType.DOCTOR) {
      const appointment = await this.appointmentRepository.findOne({
        where: {
          doctor: { id: user.id },
          patient: { id: patientId },
        },
      });
      const hasAppointments = !!appointment;

      const hasSharedEmrAccess = await this.emrService.hasDoctorSharedEmrAccess(
        user.id,
        patientId,
      );

      if (!hasAppointments && !hasSharedEmrAccess) {
        throw new ForbiddenException(
          "Not authorized to view this patient's ROS forms",
        );
      }
    }

    const rosForms = await this.rosFormRepository.find({
      where: { appointment: { patient: { id: patientId } } },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    return rosForms;
  }
}
