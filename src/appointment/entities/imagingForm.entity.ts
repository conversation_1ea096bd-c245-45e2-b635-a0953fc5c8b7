// src/appointment/entities/imagingForm.entity.ts
import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  OneToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Appointment } from './appointment.entity';

@Entity()
export class ImagingForm {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  servicesRequested: string;

  @Column({ nullable: true })
  specialInstructions: string;

  @Column({ nullable: true })
  diagnosis: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToOne(() => Appointment, (appointment) => appointment.imagingForm, {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  appointment: Appointment;
}
