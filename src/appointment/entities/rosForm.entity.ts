// src/appointment/entities/rosForm.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { Appointment } from './appointment.entity';

@Entity()
export class RosForm {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  //   GENERAL
  @Column({ nullable: true, default: null })
  recentWeightChange: boolean;

  @Column({ nullable: true, default: null })
  weakness: boolean;

  @Column({ nullable: true, default: null })
  fatigue: boolean;

  @Column({ nullable: true, default: null })
  fever: boolean;

  //   SKIN
  @Column({ nullable: true, default: null })
  skinRashes: boolean;

  @Column({ nullable: true, default: null })
  skinLumps: boolean;

  @Column({ nullable: true, default: null })
  skinSores: boolean;

  @Column({ nullable: true, default: null })
  skinItching: boolean;

  @Column({ nullable: true, default: null })
  skinDryness: boolean;

  @Column({ nullable: true, default: null })
  skinChangeInColor: boolean;

  @Column({ nullable: true, default: null })
  skinChangeInHair: boolean;

  @Column({ nullable: true, default: null })
  skinChangeInMoles: boolean;

  //   HEENT - Head
  @Column({ nullable: true, default: null })
  headache: boolean;

  @Column({ nullable: true, default: null })
  headInjury: boolean;

  @Column({ nullable: true, default: null })
  dizziness: boolean;

  @Column({ nullable: true, default: null })
  lightheadedness: boolean;

  //   HEENT - Eyes
  @Column({ nullable: true, default: null })
  visionChange: boolean;

  @Column({ nullable: true, default: null })
  glassesOrContactLenses: boolean;

  @Column({ nullable: true, default: null })
  eyePain: boolean;

  @Column({ nullable: true, default: null })
  eyeRedness: boolean;

  @Column({ nullable: true, default: null })
  excessiveTearing: boolean;

  @Column({ nullable: true, default: null })
  doubleOrBlurredVision: boolean;

  @Column({ nullable: true, default: null })
  spots: boolean;

  @Column({ nullable: true, default: null })
  specks: boolean;

  @Column({ nullable: true, default: null })
  flashingLights: boolean;

  @Column({ nullable: true, default: null })
  glaucoma: boolean;

  @Column({ nullable: true, default: null })
  cataracts: boolean;

  //   HEENT - Ears
  @Column({ nullable: true, default: null })
  hearingLoss: boolean;

  @Column({ nullable: true, default: null })
  tinnitus: boolean;

  @Column({ nullable: true, default: null })
  vertigo: boolean;

  @Column({ nullable: true, default: null })
  earaches: boolean;

  @Column({ nullable: true, default: null })
  earInfection: boolean;

  @Column({ nullable: true, default: null })
  earDischarge: boolean;

  @Column({ nullable: true, default: null })
  hearingDecreased: boolean;

  @Column({ nullable: true, default: null })
  hearingAidsUse: boolean;

  //   HEENT - Nose and Sinuses
  @Column({ nullable: true, default: null })
  nasalDischarge: boolean;

  @Column({ nullable: true, default: null })
  nasalItching: boolean;

  @Column({ nullable: true, default: null })
  frequentColds: boolean;

  @Column({ nullable: true, default: null })
  hayfever: boolean;

  @Column({ nullable: true, default: null })
  nasalStuffiness: boolean;

  @Column({ nullable: true, default: null })
  nosebleeds: boolean;

  @Column({ nullable: true, default: null })
  sinusPressurePain: boolean;

  @Column({ nullable: true, default: null })
  dentalCondition: boolean;

  @Column({ nullable: true, default: null })
  gumProblemsBleeding: boolean;

  @Column({ nullable: true, default: null })
  dentures: boolean;

  @Column({ nullable: true })
  denturesFit: string;

  @Column({ type: 'date', nullable: true })
  lastDentalExam: Date;

  @Column({ nullable: true, default: null })
  soreTongue: boolean;

  @Column({ nullable: true, default: null })
  dryMouth: boolean;

  @Column({ nullable: true, default: null })
  frequentSoreThroats: boolean;

  @Column({ nullable: true, default: null })
  hoarseness: boolean;

  //   NECK
  @Column({ nullable: true, default: null })
  swollenGlands: boolean;

  @Column({ nullable: true, default: null })
  thyroidProblems: boolean;

  @Column({ nullable: true, default: null })
  goiter: boolean;

  @Column({ nullable: true, default: null })
  neckLumps: boolean;

  @Column({ nullable: true, default: null })
  neckPainStiffness: boolean;

  //   BREASTS
  @Column({ nullable: true, default: null })
  breastLumps: boolean;

  @Column({ nullable: true, default: null })
  breastPainDiscomfort: boolean;

  @Column({ nullable: true, default: null })
  nippleDischarge: boolean;

  @Column({ nullable: true, default: null })
  selfExamPractices: boolean;

  //   RESPIRATORY
  @Column({ nullable: true, default: null })
  cough: boolean;

  @Column({ nullable: true, default: null })
  sputum: boolean;

  @Column({ nullable: true })
  sputumColor: string;

  @Column({ nullable: true })
  sputumQuantity: string;

  @Column({ nullable: true, default: null })
  sputumBlood: boolean;

  @Column({ nullable: true, default: null })
  shortnessOfBreath_respiratory: boolean;

  @Column({ nullable: true, default: null })
  wheezing: boolean;

  @Column({ nullable: true, default: null })
  pleuriticPain: boolean;

  @Column({ type: 'date', nullable: true })
  lastChestXray: Date;

  @Column({ nullable: true, default: null })
  asthma: boolean;

  @Column({ nullable: true, default: null })
  bronchitis: boolean;

  @Column({ nullable: true, default: null })
  emphysema: boolean;

  @Column({ nullable: true, default: null })
  pneumonia: boolean;

  @Column({ nullable: true, default: null })
  tuberculosis: boolean;

  //   CARDIOVASCULAR
  @Column({ nullable: true, default: null })
  heartTrouble: boolean;

  @Column({ nullable: true, default: null })
  highBloodPressure: boolean;

  @Column({ nullable: true, default: null })
  rheumaticFever: boolean;

  @Column({ nullable: true, default: null })
  heartMurmurs: boolean;

  @Column({ nullable: true, default: null })
  chestPain: boolean;

  @Column({ nullable: true, default: null })
  palpitations: boolean;

  @Column({ nullable: true, default: null })
  shortnessOfBreath_cardio: boolean;

  @Column({ nullable: true, default: null })
  orthopnea: boolean;

  @Column({ nullable: true, default: null })
  paroxysmalNocturnalDyspnea: boolean;

  @Column({ nullable: true, default: null })
  edema: boolean;

  @Column({ nullable: true })
  ekgOther: string;

  //   GASTROINTESTINAL
  @Column({ nullable: true, default: null })
  unintentionalWeightChange: boolean;

  @Column({ nullable: true, default: null })
  troubleSwallowing: boolean;

  @Column({ nullable: true, default: null })
  heartburn: boolean;

  @Column({ nullable: true, default: null })
  appetite: boolean;

  @Column({ nullable: true, default: null })
  specialDiet: boolean;

  @Column({ nullable: true, default: null })
  nausea: boolean;

  @Column({ nullable: true })
  stoolColor: string;

  @Column({ nullable: true })
  stoolSize: string;

  @Column({ nullable: true, default: null })
  changeInBowelHabits: boolean;

  @Column({ nullable: true, default: null })
  painWithDefecation: boolean;

  @Column({ nullable: true, default: null })
  rectalBleeding: boolean;

  @Column({ nullable: true, default: null })
  blackOrTarryStools: boolean;

  @Column({ nullable: true, default: null })
  hemorrhoids: boolean;

  @Column({ nullable: true, default: null })
  constipation: boolean;

  @Column({ nullable: true, default: null })
  diarrhea: boolean;

  @Column({ nullable: true, default: null })
  abdominalPain: boolean;

  @Column({ nullable: true, default: null })
  foodIntolerance: boolean;

  @Column({ nullable: true, default: null })
  excessiveBelching: boolean;

  @Column({ nullable: true, default: null })
  jaundice: boolean;

  @Column({ nullable: true, default: null })
  liverGallbladderTrouble: boolean;

  @Column({ nullable: true, default: null })
  hepatitis: boolean;

  //   PERIPHERAL VASCULAR
  @Column({ nullable: true, default: null })
  claudication: boolean;

  @Column({ nullable: true, default: null })
  legCramps: boolean;

  @Column({ nullable: true, default: null })
  varicoseVeins: boolean;

  @Column({ nullable: true, default: null })
  pastClots: boolean;

  @Column({ nullable: true, default: null })
  calfLegFeetSwelling: boolean;

  @Column({ nullable: true, default: null })
  fingertipColorChangeCold: boolean;

  @Column({ nullable: true, default: null })
  swellingRednessTendernessPeripheral: boolean;

  //   URINARY
  @Column({ nullable: true, default: null })
  frequencyOfUrination: boolean;

  @Column({ nullable: true, default: null })
  polyuria: boolean;

  @Column({ nullable: true, default: null })
  nocturia: boolean;

  @Column({ nullable: true, default: null })
  urgency: boolean;

  @Column({ nullable: true, default: null })
  burningPainDuringUrination: boolean;

  @Column({ nullable: true, default: null })
  hematuria: boolean;

  @Column({ nullable: true, default: null })
  urinaryInfections: boolean;

  @Column({ nullable: true, default: null })
  kidneyOrFlankPain: boolean;

  @Column({ nullable: true, default: null })
  kidneyStones: boolean;

  @Column({ nullable: true, default: null })
  ureteralColic: boolean;

  @Column({ nullable: true, default: null })
  suprapubicPain: boolean;

  @Column({ nullable: true, default: null })
  incontinence: boolean;

  @Column({ nullable: true, default: null })
  reducedUrinaryCaliberOrForce: boolean;

  @Column({ nullable: true, default: null })
  hesitancy: boolean;

  @Column({ nullable: true, default: null })
  dribbling: boolean;

  //   GENITAL - MALE
  @Column({ nullable: true, default: null })
  hernias: boolean;

  @Column({ nullable: true, default: null })
  penileDischarge: boolean;

  @Column({ nullable: true, default: null })
  testicularPainOrMasses: boolean;

  @Column({ nullable: true, default: null })
  scrotalPainOrSwelling: boolean;

  //   History of Sexually Transmitted Infections
  @Column({ nullable: true, default: null })
  stdHistory: boolean;

  @Column({ nullable: true })
  stdTreatment: string;

  //   Sexual Habits
  @Column({ nullable: true, default: null })
  sexualInterest: boolean;

  @Column({ nullable: true, default: null })
  sexualFunction: boolean;

  @Column({ nullable: true, default: null })
  sexualSatisfaction: boolean;

  @Column({ nullable: true, default: null })
  birthControlMethodUse: boolean;

  @Column({ nullable: true, default: null })
  condomUse: boolean;

  @Column({ nullable: true, default: null })
  sexualProblems: boolean;

  @Column({ nullable: true, default: null })
  hivInfectionConcerns: boolean;

  //   GENITAL - FEMALE
  @Column({ nullable: true })
  ageAtMenarche: string;

  @Column({ nullable: true })
  periodFrequency: string;

  @Column({ nullable: true })
  durationOfPeriods: string;

  @Column({ nullable: true })
  amountOfBleeding: string;

  @Column({ nullable: true, default: null })
  periodRegularity: boolean;

  @Column({ nullable: true, default: null })
  bleedingBetweenPeriods: boolean;

  @Column({ nullable: true, default: null })
  bleedingAfterIntercourse: boolean;

  @Column({ type: 'date', nullable: true })
  lastMenstrualPeriod: Date;

  @Column({ nullable: true, default: null })
  dysmenorrhea: boolean;

  @Column({ nullable: true, default: null })
  premenstrualTension: boolean;

  @Column({ nullable: true })
  ageAtMenopause: string;

  @Column({ nullable: true, default: null })
  menopausalSymptoms: boolean;

  @Column({ nullable: true, default: null })
  postMenopausalBleeding: boolean;

  @Column({ nullable: true, default: null })
  vaginalDischarge: boolean;

  @Column({ nullable: true, default: null })
  itching_female: boolean;

  @Column({ nullable: true, default: null })
  sores_female: boolean;

  @Column({ nullable: true, default: null })
  lumps_female: boolean;

  @Column({ nullable: true, default: null })
  historyOfSexuallyTransmittedInfections_female: boolean;

  @Column({ nullable: true })
  treatmentOfSexuallyTransmittedInfections_female: string;

  @Column({ nullable: true })
  numberOfPregnancies: string;

  @Column({ nullable: true })
  numberOfDeliveries: string;

  @Column({ nullable: true })
  typeOfDeliveries: string;

  @Column({ nullable: true })
  numberOfAbortions: string;

  @Column({ nullable: true })
  birthControlMethods: string;

  @Column({ nullable: true, default: null })
  complicationsOfPregnancy: boolean;

  //   SEXUAL HABITS (FEMALE-SPECIFIC)
  @Column({ nullable: true, default: null })
  sexualInterest_female: boolean;

  @Column({ nullable: true, default: null })
  sexualFunction_female: boolean;

  @Column({ nullable: true, default: null })
  sexualSatisfaction_female: boolean;

  @Column({ nullable: true, default: null })
  anyProblemsIncludingDyspareunia_female: boolean;

  @Column({ nullable: true, default: null })
  concernsAboutHivInfection_female: boolean;

  //   MUSCULOSKELETAL
  @Column({ nullable: true, default: null })
  muscleOrJointPain: boolean;

  @Column({ nullable: true, default: null })
  stiffness: boolean;

  @Column({ nullable: true, default: null })
  arthritis: boolean;

  @Column({ nullable: true, default: null })
  gout: boolean;

  @Column({ nullable: true, default: null })
  backache: boolean;

  @Column({ nullable: true })
  muscleLocationDescription: string;

  @Column({ nullable: true, default: null })
  swelling_muscle: boolean;

  @Column({ nullable: true, default: null })
  redness_muscle: boolean;

  @Column({ nullable: true, default: null })
  painMusculoskeletal: boolean;

  @Column({ nullable: true, default: null })
  tenderness_muscle: boolean;

  @Column({ nullable: true, default: null })
  weaknessMusculoskeletal: boolean;

  @Column({ nullable: true, default: null })
  numbnessInLimb: boolean;

  @Column({ nullable: true, default: null })
  limitationOfMotionOrActivity: boolean;

  @Column({ nullable: true, default: null })
  timingMorning: boolean;

  @Column({ nullable: true, default: null })
  timingEvening: boolean;

  @Column({ nullable: true })
  duration: string;

  @Column({ nullable: true, default: null })
  historyOfTrauma: boolean;

  @Column({ nullable: true, default: null })
  neckOrLowBackPain: boolean;

  @Column({ nullable: true, default: null })
  systemicJointPain: boolean;

  //   PSYCHIATRIC
  @Column({ nullable: true, default: null })
  nervousness: boolean;

  @Column({ nullable: true, default: null })
  tension: boolean;

  @Column({ nullable: true, default: null })
  feelingDownSadDepressed: boolean;

  @Column({ nullable: true, default: null })
  memoryChange: boolean;

  @Column({ nullable: true, default: null })
  suicidePlansOrAttempts: boolean;

  @Column({ nullable: true, default: null })
  suicidalThoughts: boolean;

  @Column({ nullable: true, default: null })
  pastCounseling: boolean;

  @Column({ nullable: true, default: null })
  psychiatricAdmissions: boolean;

  @Column({ nullable: true, default: null })
  onPsychiatricMedications: boolean;

  //   NEUROLOGIC
  @Column({ nullable: true, default: null })
  changeInMoodNeurologic: boolean;

  @Column({ nullable: true, default: null })
  changeInAttention: boolean;

  @Column({ nullable: true, default: null })
  changeInSpeech: boolean;

  @Column({ nullable: true, default: null })
  changeInOrientation: boolean;

  @Column({ nullable: true, default: null })
  changeInMemoryNeurologic: boolean;

  @Column({ nullable: true, default: null })
  changeInInsight: boolean;

  @Column({ nullable: true, default: null })
  changeInJudgement: boolean;

  @Column({ nullable: true, default: null })
  headacheNeurologic: boolean;

  @Column({ nullable: true, default: null })
  dizzinessNeurologic: boolean;

  @Column({ nullable: true, default: null })
  vertigoNeurologic: boolean;

  @Column({ nullable: true, default: null })
  fainting: boolean;

  @Column({ nullable: true, default: null })
  blackouts: boolean;

  @Column({ nullable: true, default: null })
  weaknessNeurologic: boolean;

  @Column({ nullable: true, default: null })
  paralysis: boolean;

  @Column({ nullable: true, default: null })
  numbnessOrLossOfSensation: boolean;

  @Column({ nullable: true, default: null })
  tinglingPinsAndNeedles: boolean;

  @Column({ nullable: true, default: null })
  tremorsOrOtherInvoluntaryMovement: boolean;

  @Column({ nullable: true, default: null })
  seizures: boolean;

  //   HEMATOLOGIC
  @Column({ nullable: true, default: null })
  anemia: boolean;

  @Column({ nullable: true, default: null })
  easyBruisingOrBleeding: boolean;

  @Column({ nullable: true, default: null })
  pastTransfusions: boolean;

  @Column({ nullable: true, default: null })
  transfusionReactions: boolean;

  //   ENDOCRINE
  @Column({ nullable: true, default: null })
  thyroidTrouble: boolean;

  @Column({ nullable: true, default: null })
  heatOrColdIntolerance: boolean;

  @Column({ nullable: true, default: null })
  excessiveSweating: boolean;

  @Column({ nullable: true, default: null })
  excessiveThirstOrHunger: boolean;

  @Column({ nullable: true, default: null })
  polyuriaEndocrine: boolean;

  @Column({ nullable: true, default: null })
  changeInGloveOrShoeSize: boolean;

  @OneToOne(() => Appointment, (appointment) => appointment.rosForm, {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  appointment: Appointment;
}
