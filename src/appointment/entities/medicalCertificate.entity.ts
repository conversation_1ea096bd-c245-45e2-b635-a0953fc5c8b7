// src/appointment/entities/medicalCertificate.entity.ts
import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  OneToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Appointment } from './appointment.entity';

@Entity()
export class MedicalCertificate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  diagnosis: string;

  @Column({ nullable: true })
  recommendations: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToOne(
    () => Appointment,
    (appointment) => appointment.medicalCertificate,
    {
      onDelete: 'CASCADE',
    },
  )
  @JoinColumn()
  appointment: Appointment;
}
