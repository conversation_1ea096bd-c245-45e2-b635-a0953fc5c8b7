// src/appointment/entities/soapForm.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Appointment } from './appointment.entity';

@Entity()
export class SoapForm {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  cc: string;

  @Column()
  historyOfPatientIllness: string;

  @Column()
  ros: string;

  @Column()
  pmh: string;

  @Column()
  psh: string;

  @Column()
  meds: string;

  @Column()
  allergies: string;

  @Column()
  fh: string;

  @Column()
  sh: string;

  @Column()
  vitalSigns: string;

  @Column()
  gen: string;

  @Column()
  heart: string;

  @Column()
  lungs: string;

  @Column()
  abdomen: string;

  @Column()
  otherSystem: string;

  @Column()
  ose: string;

  @Column()
  patientFirstProblem: string;

  @Column()
  patientSecondProblem: string;

  @Column()
  patientThirdProblem: string;

  @Column()
  somaticDysfunction: string;

  @Column()
  other: string;

  @Column()
  sympotomatic: string;

  @Column()
  diagnosticTests: string;

  @Column()
  testsNextSteps: string;

  @Column()
  prescribeMeds: string;

  @Column()
  omtPerformed: string;

  @Column()
  education: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToOne(() => Appointment, (appointment) => appointment.soapForm, {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  appointment: Appointment;
}
