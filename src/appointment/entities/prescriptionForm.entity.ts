// src/appointment/entities/prescriptionForm.entity.ts
import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Appointment } from './appointment.entity';

@Entity()
export class PrescriptionForm {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  drugName: string;

  @Column()
  duration: string;

  @Column()
  dosage: string;

  @Column()
  frequency: string;

  @Column({ nullable: true })
  instructions: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(
    () => Appointment,
    (appointment) => appointment.prescriptionForms,
    {
      onDelete: 'CASCADE',
    },
  )
  appointment: Appointment;
}
