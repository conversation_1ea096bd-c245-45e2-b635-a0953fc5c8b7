// src/appointment/entities/compliance.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Appointment } from './appointment.entity';

export enum ComplianceType {
  DOCTOR = 'doctor',
  PATIENT = 'patient',
}

@Entity()
export class Compliance {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: ComplianceType,
  })
  type: ComplianceType;

  @Column()
  log: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(
    () => Appointment,
    (appointment) => appointment.compliances,
    {
      onDelete: 'CASCADE',
    },
  )
  appointment: Appointment;
}
