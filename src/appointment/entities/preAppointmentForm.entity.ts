// src/appointment/entities/preAppointmentForm.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { Appointment } from './appointment.entity';

export enum ConstitutionalSymptoms {
  FEVER = 'fever',
  WEIGHTLOSS = 'weightLoss',
  EXTREME_FATIGUE = 'extremeFatigue',
}

export enum EyeProblems {
  DOUBLE_VISION = 'doubleVision',
  SUDDEN_LOSS_OF_VISION = 'suddenLossOfVision',
}

export enum EnmtProblems {
  SORE_THROAT = 'soreThroat',
  RUNNY_NOSE = 'runnyNose',
  EAR_PAIN = 'earPain',
}

export enum CardiovascularProblems {
  CHEST_PAIN = 'chestPain',
  PALPITATIONS = 'palpitations',
}

export enum RespiratoryProblems {
  COUGH = 'cough',
  WHEEZING = 'wheezing',
  SHORTNESS_OF_BREATH = 'shotnessOfBreath',
}

export enum GastrointestinalProblems {
  NAUSEA = 'nausea',
  VOMITING = 'vomiting',
  ABDOMINAL_PAIN = 'abdominalPain',
  CONSTIPATION = 'constipation',
  DIARRHEA = 'diarrhea',
  BLOOD_IN_STOOLS = 'bloodInStools',
}

export enum GenitourinaryProblems {
  IRREGURAL_MENSES = 'irregularMenses',
  VAGINAL_BLEEDING_AFTER_MENOPAUSE = 'vaginalBleeding',
  BLOODY_URINE = 'abdominalPain',
  FREQUENT_OR_PAINFUL_URINATION = 'constipation',
  IMPOTENCE = 'diarrhea',
}

export enum SkinProblems {
  RASH = 'rash',
  CHANGING_MOLE = 'changingMole',
}

export enum SleepProblems {
  SNORING = 'snoring',
  DIFFICULTY_SLEEPING = 'difficultySleeping',
}

export enum NeurologicalProblems {
  HEADACHE = 'headache',
  FALLING = 'falling',
  NUMBNESS_ONE_SIDE = 'numbnessOneSide',
}

export enum MusculoskeletalProblems {
  JOINT_PAIN = 'jointPain',
  MUSCLE_WEAKNESS = 'muscleWeakness',
  PSYCHIATRIC = 'psychiatric',
}

export enum PsychiatricProblems {
  DEPRESSION = 'depression',
  ANXIETY = 'anxiety',
  SUICIDAL_THOUGHTS = 'suicidalThoughts',
}

export enum EndocrineProblems {
  EXCESSIVE_THRIST = 'excessiveThirst',
  COLD_HEAT_TOLERANCE = 'coldHeatTolerance',
  BREAST_MASS = 'breastMass',
}

export enum HematologicProblems {
  UNUSUAL_BRUISING_BLEEDING = 'unusualBruisingBleeding',
  ENLARGED_LYMPH_NODES = 'enlargedLymphNodes',
}

export enum AllergicProblems {
  HIGH_FEVER = 'highFever',
}

@Entity()
export class PreAppointmentForm {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ default: false })
  isMedCertificateRequested: boolean;

  @Column({ nullable: true })
  agendaForAppointment: string;

  @Column({ nullable: true })
  goalForHealth: string;

  //   DIABETES
  @Column({ nullable: true })
  diabetes_isProblemWithMedication: boolean;

  @Column({ nullable: true })
  diabetes_homeGlucoseReadings: string;

  //   HIGH BP
  @Column({ nullable: true })
  highBloodPressure_isProblemWithMedication: boolean;

  @Column({ nullable: true })
  highBloodPressure_homeBpReadings: string;

  //   DEPRESSION
  @Column({ nullable: true })
  depression_isProblemWithMedication: boolean;

  @Column({ nullable: true })
  depression_isSuicidalThoughts: boolean;

  //   HIGH CHOLESTROL
  @Column({ nullable: true })
  highCholesterol_isProblemWithMedication: boolean;

  //   OTHER VISITS
  @Column({ nullable: true })
  otherMedVisit: boolean;

  @Column({ nullable: true })
  otherMedVisit_details: string;

  //   EXERCISE
  @Column({ nullable: true })
  exercice: string;

  @Column({ nullable: true })
  exercice_howLong: string;

  @Column({ nullable: true })
  exercice_howOften: string;

  //   SHORTNESS OF BREATH
  @Column({ nullable: true })
  isShortnessOfBreath: boolean;

  //   SMOKING
  @Column({ nullable: true })
  smoking_isInterestedInQuitting: boolean;

  @Column({ nullable: true })
  smoking_howMuch: string;

  // FALLS
  @Column({ nullable: true })
  fallenInPastyear: boolean;

  @Column({ nullable: true })
  problemWithWalkingBalance: string;

  //   ALCOHOL
  @Column({ nullable: true })
  alcohol_numberOfDrinks_week: string;

  @Column({ nullable: true })
  alcohol_numberOfDrinks_day: string;

  @Column({ nullable: true })
  alcohol_moreThanFourDrinks: boolean;

  @Column({ nullable: true })
  alcohol_othersConcerned: boolean;

  //   CAFFEINE
  @Column({ nullable: true })
  caffeinePerDay: string;

  //   BIRTH CONTROL
  @Column({ nullable: true })
  birthControlMethod: string;

  //   SAFETY
  @Column({ nullable: true })
  isUnsafeRelationship: boolean;

  @Column({ nullable: true })
  isWearSeatbelt: boolean;

  //   HIV
  @Column({ nullable: true })
  isHivTestRequested: boolean;

  //   SLEEP
  @Column({ nullable: true })
  isSleepApnea: boolean;

  //   DEPRESSION SCREEN
  @Column({ nullable: true })
  isFeelingDown: boolean;

  //   MEDICATION
  @Column({ nullable: true })
  isProblemWithMedication: boolean;

  @Column({ nullable: true })
  problemWithMedicationDetails: string;

  //   BLADDER CONTROL
  @Column({ nullable: true })
  isProblemBladderControl: boolean;

  //   END OF LIFE CARE
  @Column({ nullable: true })
  discussEndOfLifeCare: boolean;

  //   UPDATE
  @Column({ nullable: true })
  newIllnessInFamily: string;

  @Column({ nullable: true })
  newDrugAllergies: string;

  @Column({
    type: 'enum',
    enum: ConstitutionalSymptoms,
    array: true,
    nullable: true,
  })
  constitutionalSymptoms: ConstitutionalSymptoms[];

  @Column({
    type: 'enum',
    enum: EyeProblems,
    array: true,
    nullable: true,
  })
  eyeProblems: EyeProblems[];

  @Column({
    type: 'enum',
    enum: EnmtProblems,
    array: true,
    nullable: true,
  })
  enmtProblems: EnmtProblems[];

  @Column({
    type: 'enum',
    enum: CardiovascularProblems,
    array: true,
    nullable: true,
  })
  cardiovascularProblems: CardiovascularProblems[];

  @Column({
    type: 'enum',
    enum: RespiratoryProblems,
    array: true,
    nullable: true,
  })
  respiratoryProblems: RespiratoryProblems[];

  @Column({
    type: 'enum',
    enum: GastrointestinalProblems,
    array: true,
    nullable: true,
  })
  gastrointestinalProblems: GastrointestinalProblems[];

  @Column({
    type: 'enum',
    enum: GenitourinaryProblems,
    array: true,
    nullable: true,
  })
  genitourinaryProblems: GenitourinaryProblems[];

  @Column({
    type: 'enum',
    enum: SkinProblems,
    array: true,
    nullable: true,
  })
  skinProblems: SkinProblems[];

  @Column({
    type: 'enum',
    enum: SleepProblems,
    array: true,
    nullable: true,
  })
  sleepProblems: SleepProblems[];

  @Column({
    type: 'enum',
    enum: NeurologicalProblems,
    array: true,
    nullable: true,
  })
  neurologicalProblems: NeurologicalProblems[];

  @Column({
    type: 'enum',
    enum: MusculoskeletalProblems,
    array: true,
    nullable: true,
  })
  musculoskeletalProblems: MusculoskeletalProblems[];

  @Column({
    type: 'enum',
    enum: PsychiatricProblems,
    array: true,
    nullable: true,
  })
  psychiatricProblems: PsychiatricProblems[];

  @Column({
    type: 'enum',
    enum: EndocrineProblems,
    array: true,
    nullable: true,
  })
  endocrineProblems: EndocrineProblems[];

  @Column({
    type: 'enum',
    enum: HematologicProblems,
    array: true,
    nullable: true,
  })
  hematologicProblems: HematologicProblems[];

  @Column({
    type: 'enum',
    enum: AllergicProblems,
    array: true,
    nullable: true,
  })
  allergicProblems: AllergicProblems[];

  @OneToOne(
    () => Appointment,
    (appointment) => appointment.preAppointmentForm,
    {
      onDelete: 'CASCADE',
    },
  )
  @JoinColumn()
  appointment: Appointment;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
