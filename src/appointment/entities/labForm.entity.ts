// src/appointment/entities/labForm.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  OneToOne,
  Join<PERSON>olumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Appointment } from './appointment.entity';

export enum Chemistry1Tests {
  GLUCOSE_FASTING = 'glucoseFasting',
  GLUCOSE_RANDOM = 'glucoseRandom',
  ELECTROLYTES_CO2 = 'electrolytesCO2',
  CREATININE_EGFR = 'creatinineEgfr',
  CALCIUM_TOTAL = 'calciumTotal',
  TOTAL_PROTEIN = 'totalProtein',
  ALBUMIN = 'albumin',
  TOTAL_BILIRUBIN = 'totalBilirubin',
  ALK_PHOS_ALP = 'alkPhosAlp',
  ALT = 'alt',
  GGT = 'ggt',
  LD = 'ld',
  LIPASE = 'lipase',
  CK = 'ck',
  SERUM_PREGNANCY = 'serumPregnancy',
  BILIRUBIN_DIRECT = 'bilirubinDirect',
  CRP = 'crp',
  AMMONIA = 'ammonia',
  CALCIUM_IONIZED = 'calciumIonized',
  OSMOLALITY = 'osmolality',
  LACTATE_GREEN_ON_ICE = 'lactateGreenOnIce',
  URIC_ACID_URATE = 'uricAcidUrate',
  MAGNESIUM = 'magnesium',
  PHOSPHATE = 'phosphate',
}

export enum HematologyTests {
  CBC_AUTO_DITT = 'cbcAutoDitt',
  MONO_SCREEN = 'monoScreen',
  KLEIHAUER = 'kleihauer',
  A1C = 'a1c',
}

export enum CoagulationTests {
  PTT = 'ptt',
  D_DIMER = 'dDimer',
  FIBRINOGEN = 'fibrinogen',
}

export enum ImmunologyTests {
  TISSUE_TRANGLUTAMINE = 'tissueTransglutamine',
  COMPLEMENT_C3_C4 = 'complementC3C4',
  FARMERS_LUNG = 'farmersLung',
  PROTEIN = 'protein',
  SERUM_FREE_LIGHT_CHAINS = 'serumFreeLightChains',
  AMA = 'ama',
  VASCULITIS_MPO_PR3 = 'vasculitisMpoPr3',
  IGG_IGA_IGM = 'iggIgaIgm',
  DS_DNA = 'dsDNA',
  RHEUMATOID_FACTOR = 'rheumatoidFactor',
  ANTI_GBM = 'antiGbm',
  HAPTOGLOBIN = 'haptoglobin',
  CCP_CITROINE_AB = 'ccpCitroineAb',
  ASOT = 'asot',
  CARDIOLIPIN = 'cardiolipin',
  MICROGLOBULIN = 'microglobulin',
  U1_ANTITRYPSIN = 'u1Antitrypsin',
}

export enum CardiacFunctionAndLipidTests {
  HS_CRP_CARDIAC = 'hsCrpCardiac',
  BNP_PURPLE_TUBE = 'bnpPurpleTube',
  TROPONIN_GREEN_TUBE = 'troponinGreenTube',
  PASSING_NON_PASSING = 'passingNonPassing',
  LDL_HDL_TRIGLYCERIDES = 'ldlHdlTriglycerides',
}

export enum ToleranceTests {
  SEVENTY_FIVE_G_DIABETIC_CONFIRMATORY = '75gDiabeticConfirmatory',
  LACTOSE_TOLERANCE_TEST = 'lactoseToleranceTest',
  SEVENTY_FIVE_G_GESTATIONAL_CONFIRMATORY = '75gGestationalConfirmatory',
  FIFTY_G_GESTATIONAL_SCREEN = '50gGestationalScreen',
  SEVENTY_FIVE_G_POST_PARTUM_SCREEN = '75gPostPartumScreen',
}

export enum NutritionalStatusTests {
  FERRITIN = 'ferritin',
  IRON_STUDIES = 'ironStudies',
  VITAMIN_B12 = 'vitaminB12',
  PREALBUMIN = 'prealbumin',
}

export enum EndocrineAndTumorMarkers {
  PROLACTIN = 'prolactin',
  PROGESTERONE = 'progesterone',
  CA_125 = 'ca125',
  DHEAS = 'dheas',
  ESTRADIOL = 'estradiol',
  CEA = 'cea',
  CORTISOL_H_S = 'cortisolHS',
  FSH = 'fsh',
  AFP = 'afp',
  PTH_INTACT_RED_TUBE = 'pthIntactRedTube',
  LH = 'lh',
  CA_19_9 = 'ca199',
  PSA_40_TO_75_YRS = 'psa40To75Yrs',
  CA_15_3 = 'ca153',
  TESTOSTERONE_TOTAL = 'testosteroneTotal',
  TSH_DIAGNOSTIC = 'tshDiagnostic',
  TSH_MONITOR_TX = 'tshMonitorTx',
}

export enum SerumToxicologyTests {
  ETHANOL = 'ethanol',
  ACETAMINOPHEN = 'acetaminophen',
  SALICYLATE = 'salicylate',
  TRICYCLICS_SCREEN = 'tricyclicsScreen',
}

export enum BloodGasesTests {
  CARBOXYHEMOGLOBIN_CO = 'carboxyhemoglobinCo',
  METHEMOGLOBIN = 'methemoglobin',
  LACTATE = 'lactate',
  PREALBUMIN = 'prealbumin',
  SPECIMEN_ARTERIAL_CAPILLARY = 'specimenArterialCapillary',
}

export enum PaymentResponsibility {
  SELF = 'self',
  INSURANCE = 'insurance',
}

@Entity()
export class LabForm {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  //   PAYMENT RESPONSIBILITY
  @Column({
    type: 'enum',
    enum: PaymentResponsibility,
    nullable: true,
  })
  paymentResponsibility: PaymentResponsibility;

  //   FASTING
  @Column({ nullable: true })
  fastingRequired: boolean;

  //   CHEMISTRY-1
  @Column({
    type: 'enum',
    enum: Chemistry1Tests,
    array: true,
    nullable: true,
  })
  chemistry1Tests: Chemistry1Tests[];

  //   HEMATOLOGY
  @Column({
    type: 'enum',
    enum: HematologyTests,
    array: true,
    nullable: true,
  })
  hematologyTests: HematologyTests[];

  //   COAGULATION
  @Column({
    type: 'enum',
    enum: CoagulationTests,
    array: true,
    nullable: true,
  })
  coagulationTests: CoagulationTests[];

  //   IMMUNOLOGY
  @Column({
    type: 'enum',
    enum: ImmunologyTests,
    array: true,
    nullable: true,
  })
  immunologyTests: ImmunologyTests[];

  //   CARDIAC FUNCTION AND LIPIDS
  @Column({
    type: 'enum',
    enum: CardiacFunctionAndLipidTests,
    array: true,
    nullable: true,
  })
  cardiacFunctionAndLipidTests: CardiacFunctionAndLipidTests[];

  //   TOLERANCE TEST
  @Column({
    type: 'enum',
    enum: ToleranceTests,
    array: true,
    nullable: true,
  })
  toleranceTests: ToleranceTests[];

  //   NUTRITIONAL STATUS
  @Column({
    type: 'enum',
    enum: NutritionalStatusTests,
    array: true,
    nullable: true,
  })
  nutritionalStatusTests: NutritionalStatusTests[];

  //   ENDOCRINE AND TUMOR MARKERS
  @Column({
    type: 'enum',
    enum: EndocrineAndTumorMarkers,
    array: true,
    nullable: true,
  })
  endocrineAndTumorMarkers: EndocrineAndTumorMarkers[];

  //   SERUM TOXICOLOGY
  @Column({
    type: 'enum',
    enum: SerumToxicologyTests,
    array: true,
    nullable: true,
  })
  serumToxicologyTests: SerumToxicologyTests[];

  //   BLOOD GASES
  @Column({
    type: 'enum',
    enum: BloodGasesTests,
    array: true,
    nullable: true,
  })
  bloodGases: BloodGasesTests[];

  @Column({ nullable: true })
  o2Device: string;

  @Column({ nullable: true })
  o2Therapy: string;

  @Column({ nullable: true })
  temprature: string;

  //   DATE & TIME OF LAST DOSE REQUIRED
  @Column({ type: 'timestamp', nullable: true })
  digoxinLastDose?: Date;

  @Column({ type: 'timestamp', nullable: true })
  lithiumLastDose?: Date;

  @Column({ type: 'timestamp', nullable: true })
  phenobarbitalLastDose?: Date;

  @Column({ type: 'timestamp', nullable: true })
  phenytoinLastDose?: Date;

  @Column({ type: 'timestamp', nullable: true })
  primidoneLastDose?: Date;

  @Column({ type: 'timestamp', nullable: true })
  valproicAcidLastDose?: Date;

  @Column({ type: 'timestamp', nullable: true })
  cyclosporineLastDose?: Date;

  @Column({ type: 'timestamp', nullable: true })
  vancomycinLastDose?: Date;

  @Column({ type: 'timestamp', nullable: true })
  preDoseLastDose?: Date;

  @Column({ type: 'timestamp', nullable: true })
  postDoseLastDose?: Date;

  @Column({ type: 'timestamp', nullable: true })
  gentamicinLastDose?: Date;

  @Column({ type: 'timestamp', nullable: true })
  tobramycinLastDose?: Date;

  @Column({ type: 'timestamp', nullable: true })
  preDoseLevelLastDose?: Date;

  @Column({ type: 'timestamp', nullable: true })
  extendedIntervalPediatricsLastDose?: Date;

  @Column({ type: 'timestamp', nullable: true })
  postDoseLevelLastDose?: Date;

  @Column({ type: 'timestamp', nullable: true })
  extendedIntervalLastDose?: Date;

  @Column({ type: 'timestamp', nullable: true })
  hr22PostLevelNeonatesLastDose?: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToOne(() => Appointment, (appointment) => appointment.labForm, {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  appointment: Appointment;
}
