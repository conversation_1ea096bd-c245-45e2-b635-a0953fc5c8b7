// src/appointment/entities/appointment.entity.ts
import { Consultation } from 'src/consultation/entities/consultation.entity';
import { Doctor } from 'src/doctor/entities/doctor.entity';
import { Patient } from 'src/patient/entities/patient.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToOne,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { PrescriptionForm } from './prescriptionForm.entity';
import { ImagingForm } from './imagingForm.entity';
import { MedicalCertificate } from './medicalCertificate.entity';
import { PreAppointmentForm } from './preAppointmentForm.entity';
import { SoapForm } from './soapForm.entity';
import { LabForm } from './labForm.entity';
import { RosForm } from './rosForm.entity';
import { Compliance } from './compliance.entity';
import { VideoCall } from 'src/videoCall/entities/videoCall.entity';

export enum AppointmentStatus {
  UPCOMING = 'upcoming',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum CancelledBy {
  DOCTOR = 'doctor',
  PATIENT = 'patient',
  ADMIN = 'admin',
}

@Entity()
export class Appointment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'timestamp' })
  startDateTime: Date;

  @Column({ type: 'timestamp' })
  endDateTime: Date;

  @Column({
    type: 'enum',
    enum: AppointmentStatus,
    default: AppointmentStatus.UPCOMING,
  })
  appointmentStatus: AppointmentStatus;

  @Column({ nullable: true })
  cancelReason: string;

  @Column({ nullable: true })
  cancelledBy: string;

  @ManyToOne(() => Doctor, (doctor) => doctor.appointments, {
    onDelete: 'CASCADE',
  })
  doctor: Doctor;

  @ManyToOne(() => Consultation, (consultation) => consultation.appointments, {
    onDelete: 'CASCADE',
  })
  consultation: Consultation;

  @ManyToOne(() => Patient, (patient) => patient.appointments, {
    onDelete: 'CASCADE',
  })
  patient: Patient;

  //   HAS OWN CONTROLLER AND SERVICE
  @OneToMany(
    () => PrescriptionForm,
    (prescriptionForm) => prescriptionForm.appointment,
  )
  prescriptionForms: PrescriptionForm[];

  //   HAS OWN CONTROLLER AND SERVICE
  @OneToMany(() => Compliance, (compliance) => compliance.appointment)
  compliances: Compliance[];

  //   HAS OWN CONTROLLER AND SERVICE
  @OneToOne(() => ImagingForm, (imagingForm) => imagingForm.appointment)
  imagingForm: ImagingForm;

  //   HAS OWN CONTROLLER AND SERVICE
  @OneToOne(
    () => MedicalCertificate,
    (medicalCertificate) => medicalCertificate.appointment,
  )
  medicalCertificate: MedicalCertificate;

  //   HAS OWN CONTROLLER AND SERVICE
  @OneToOne(
    () => PreAppointmentForm,
    (preAppointmentForm) => preAppointmentForm.appointment,
  )
  preAppointmentForm: PreAppointmentForm;

  //   HAS OWN CONTROLLER AND SERVICE
  @OneToOne(() => SoapForm, (soapForm) => soapForm.appointment)
  soapForm: SoapForm;

  //   HAS OWN CONTROLLER AND SERVICE
  @OneToOne(() => LabForm, (labForm) => labForm.appointment)
  labForm: LabForm;

  //   HAS OWN CONTROLLER AND SERVICE
  @OneToOne(() => RosForm, (rosForm) => rosForm.appointment)
  rosForm: RosForm;

  //   HAS OWN MODULE, CONTROLLER AND SERVICE
  @OneToOne(() => VideoCall, (videoCall) => videoCall.appointment)
  videoCall: VideoCall;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
