// src/appointment/appointment.module.ts
import { forwardRef, Module } from '@nestjs/common';
import { AppointmentService } from './appointment.service';
import { AppointmentController } from './appointment.controller';
import { PreAppointmentFormService } from './preAppointmentForm.service';
import { PreAppointmentFormController } from './preAppointmentForm.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Appointment } from './entities/appointment.entity';
import { ImagingForm } from './entities/imagingForm.entity';
import { MedicalCertificate } from './entities/medicalCertificate.entity';
import { PreAppointmentForm } from './entities/preAppointmentForm.entity';
import { PrescriptionForm } from './entities/prescriptionForm.entity';
import { SoapForm } from './entities/soapForm.entity';
import { RosForm } from './entities/rosForm.entity';
import { LabForm } from './entities/labForm.entity';
import { Compliance } from './entities/compliance.entity';
import { AuthModule } from 'src/auth/auth.module';
import { DoctorModule } from 'src/doctor/doctor.module';
import { PatientModule } from 'src/patient/patient.module';
import { ConsultationModule } from 'src/consultation/consultation.module';
import { ScheduleModule } from 'src/schedule/schedule.module';
import { EmrModule } from 'src/emr/emr.module';
import { ImagingFormController } from './imagingForm.controller';
import { ImagingFormService } from './imagingForm.service';
import { LabFormController } from './labForm.controller';
import { LabFormService } from './labForm.service';
import { MedicalCertificateController } from './medicalCertificate.controller';
import { MedicalCertificateService } from './medicalCertificate.service';
import { SoapFormController } from './soapForm.controller';
import { SoapFormService } from './soapForm.service';
import { RosFormController } from './rosForm.controller';
import { RosFormService } from './rosForm.service';
import { PrescriptionFormController } from './prescriptionForm.controller';
import { PrescriptionFormService } from './prescriptionForm.service';
import { ComplianceController } from './compliance.controller';
import { ComplianceService } from './compliance.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Appointment,
      ImagingForm,
      LabForm,
      MedicalCertificate,
      PreAppointmentForm,
      PrescriptionForm,
      RosForm,
      SoapForm,
      Compliance,
    ]),
    forwardRef(() => AuthModule),
    forwardRef(() => DoctorModule),
    forwardRef(() => PatientModule),
    forwardRef(() => ConsultationModule),
    forwardRef(() => ScheduleModule),
    forwardRef(() => EmrModule),
  ],
  controllers: [
    AppointmentController,
    PreAppointmentFormController,
    ImagingFormController,
    LabFormController,
    MedicalCertificateController,
    SoapFormController,
    RosFormController,
    PrescriptionFormController,
    ComplianceController,
  ],
  providers: [
    AppointmentService,
    PreAppointmentFormService,
    ImagingFormService,
    LabFormService,
    MedicalCertificateService,
    SoapFormService,
    RosFormService,
    PrescriptionFormService,
    ComplianceService,
  ],
  exports: [
    AppointmentService,
    PreAppointmentFormService,
    ImagingFormService,
    LabFormService,
    MedicalCertificateService,
    SoapFormService,
    RosFormService,
    PrescriptionFormService,
    ComplianceService,
  ],
})
export class AppointmentModule {}
