// src/appointment/compliance.controller.ts
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ComplianceService } from './compliance.service';
import { CreateComplianceDto } from './dtos/create-compliance.dto';
import { UpdateComplianceDto } from './dtos/update-compliance.dto';
import { AuthenticationGuard } from 'src/auth/guards/authentication.guard';
import { RequestWithUser } from 'src/common/interfaces/requestWithUser.interface';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';

@UseGuards(AuthenticationGuard)
@Controller('api/compliance')
export class ComplianceController {
  // CONSTRUCTOR
  constructor(private readonly complianceService: ComplianceService) {}

  // CONTROLLERS
  @Post('create')
  async createCompliance(
    @Body() createComplianceDto: CreateComplianceDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.complianceService.createCompliance(
      user,
      createComplianceDto,
    );
    return { message: 'Compliance created successfully', data };
  }

  @Put('update')
  async updateCompliance(
    @Body() updateComplianceDto: UpdateComplianceDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.complianceService.updateCompliance(
      user,
      updateComplianceDto,
    );
    return { message: 'Compliance updated successfully', data };
  }

  @Delete('delete/:id')
  async deleteCompliance(
    @Param('id') id: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    await this.complianceService.deleteCompliance(user, id);
    return { message: 'Compliance deleted successfully' };
  }

  @Get('getById/:id')
  async getComplianceById(
    @Param('id') id: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.complianceService.getComplianceById(user, id);
    return { message: 'Compliance fetched successfully', data };
  }

  @Get('getByAppointmentId/:appointmentId')
  async getCompliancesByAppointmentId(
    @Param('appointmentId') appointmentId: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.complianceService.getCompliancesByAppointmentId(
      user,
      appointmentId,
    );
    return { message: 'Compliances fetched successfully', data };
  }

  @Get('getDoctorCompliancesByAppointmentId/:appointmentId')
  async getDoctorCompliancesByAppointmentId(
    @Param('appointmentId') appointmentId: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.complianceService.getDoctorCompliancesByAppointmentId(
      user,
      appointmentId,
    );
    return { message: 'Doctor compliances fetched successfully', data };
  }

  @Get('getPatientCompliancesByAppointmentId/:appointmentId')
  async getPatientCompliancesByAppointmentId(
    @Param('appointmentId') appointmentId: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.complianceService.getPatientCompliancesByAppointmentId(
      user,
      appointmentId,
    );
    return { message: 'Patient compliances fetched successfully', data };
  }
}
