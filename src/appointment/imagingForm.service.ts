// src/appointment/imagingForm.service.ts
import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ImagingForm } from './entities/imagingForm.entity';
import { Appointment, AppointmentStatus } from './entities/appointment.entity';
import { CreateImagingFormDto } from './dtos/create-imagingForm.dto';
import { UpdateImagingFormDto } from './dtos/update-imagingForm.dto';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';
import { UserType } from 'src/common/enums/userType.enum';
import { EmrService } from 'src/emr/emr.service';

@Injectable()
export class ImagingFormService {
  // CONSTRUCTOR
  constructor(
    @InjectRepository(ImagingForm)
    private readonly imagingFormRepository: Repository<ImagingForm>,
    @InjectRepository(Appointment)
    private readonly appointmentRepository: Repository<Appointment>,
    @Inject(forwardRef(() => EmrService))
    private readonly emrService: EmrService,
  ) {}

  // SERVICES
  async createImagingForm(
    user: DoctorWithUserType,
    createImagingFormDto: CreateImagingFormDto,
  ): Promise<ImagingForm> {
    const { appointmentId, ...formData } = createImagingFormDto;

    // FIND APPOINTMENT WITH RELATIONS
    const appointment = await this.appointmentRepository.findOne({
      where: { id: appointmentId },
      relations: [
        'patient',
        'patient.profilePicture',
        'doctor',
        'doctor.profilePicture',
        'doctor.signature',
        'imagingForm',
      ],
    });

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    // CHECK IF USER IS THE BOOKED DOCTOR
    if (appointment.doctor.id !== user.id) {
      throw new ForbiddenException(
        'Only the booked doctor can create imaging forms',
      );
    }

    // CHECK IF APPOINTMENT STATUS IS UPCOMING OR COMPLETED
    if (
      appointment.appointmentStatus !== AppointmentStatus.UPCOMING &&
      appointment.appointmentStatus !== AppointmentStatus.COMPLETED
    ) {
      throw new BadRequestException(
        'Imaging forms can only be created for upcoming or completed appointments',
      );
    }

    // ENSURE ONLY ONE FORM PER APPOINTMENT - CHECK IF FORM ALREADY EXISTS
    if (appointment.imagingForm) {
      throw new BadRequestException(
        'Imaging form already exists for this appointment. Use update instead.',
      );
    }

    // DOUBLE CHECK - QUERY DIRECTLY TO ENSURE NO FORM EXISTS
    const existingForm = await this.imagingFormRepository.findOne({
      where: { appointment: { id: appointmentId } },
    });

    if (existingForm) {
      throw new BadRequestException(
        'Imaging form already exists for this appointment. Use update instead.',
      );
    }

    // CREATE FORM
    const imagingForm = this.imagingFormRepository.create({
      ...formData,
      appointment,
    });

    return await this.imagingFormRepository.save(imagingForm);
  }
  async updateImagingForm(
    user: DoctorWithUserType,
    updateImagingFormDto: UpdateImagingFormDto,
  ): Promise<ImagingForm> {
    const { id, appointmentId, ...updateData } = updateImagingFormDto;

    // VALIDATE THAT EITHER ID OR APPOINTMENTID IS PROVIDED
    if (!id && !appointmentId) {
      throw new BadRequestException(
        'Either form id or appointmentId must be provided',
      );
    }

    let imagingForm: ImagingForm;

    if (id) {
      // FIND BY FORM ID
      imagingForm = await this.imagingFormRepository.findOne({
        where: { id },
        relations: [
          'appointment',
          'appointment.patient',
          'appointment.patient.profilePicture',
          'appointment.doctor',
          'appointment.doctor.profilePicture',
          'appointment.doctor.signature',
        ],
      });
    } else {
      // FIND BY APPOINTMENT ID
      imagingForm = await this.imagingFormRepository.findOne({
        where: { appointment: { id: appointmentId } },
        relations: [
          'appointment',
          'appointment.patient',
          'appointment.patient.profilePicture',
          'appointment.doctor',
          'appointment.doctor.profilePicture',
          'appointment.doctor.signature',
        ],
      });
    }

    if (!imagingForm) {
      throw new NotFoundException('Imaging form not found');
    }

    // CHECK IF USER IS THE BOOKED DOCTOR
    if (imagingForm.appointment.doctor.id !== user.id) {
      throw new ForbiddenException(
        'Only the booked doctor can edit imaging forms',
      );
    }

    // CHECK IF APPOINTMENT STATUS IS UPCOMING OR COMPLETED
    if (
      imagingForm.appointment.appointmentStatus !==
        AppointmentStatus.UPCOMING &&
      imagingForm.appointment.appointmentStatus !== AppointmentStatus.COMPLETED
    ) {
      throw new BadRequestException(
        'Imaging forms can only be edited for upcoming or completed appointments',
      );
    }

    // UPDATE FORM
    Object.assign(imagingForm, updateData);
    return await this.imagingFormRepository.save(imagingForm);
  }
  async getImagingFormById(
    user: DoctorWithUserType | PatientWithUserType,
    id: string,
  ): Promise<ImagingForm> {
    // FIND FORM WITH RELATIONS
    const imagingForm = await this.imagingFormRepository.findOne({
      where: { id },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    if (!imagingForm) {
      throw new NotFoundException('Imaging form not found');
    }

    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN FORMS
      if (imagingForm.appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'You can only view imaging forms for your own appointments',
        );
      }
    }

    // DOCTORS CAN VIEW FORMS IF THEY ARE THE BOOKED DOCTOR OR HAVE SHARED EMR ACCESS
    if (user.userType === UserType.DOCTOR) {
      const isBookedDoctor = imagingForm.appointment.doctor.id === user.id;

      if (!isBookedDoctor) {
        const hasSharedEmrAccess =
          await this.emrService.hasDoctorSharedEmrAccess(
            user.id,
            imagingForm.appointment.patient.id,
          );

        if (!hasSharedEmrAccess) {
          throw new ForbiddenException(
            'Not authorized to view this imaging form',
          );
        }
      }
    }

    return imagingForm;
  }
  async getImagingFormByAppointmentId(
    user: DoctorWithUserType | PatientWithUserType,
    appointmentId: string,
  ): Promise<ImagingForm> {
    // FIND APPOINTMENT FOR AUTHORIZATION CHECK
    const appointment = await this.appointmentRepository.findOne({
      where: { id: appointmentId },
      relations: ['patient', 'doctor'],
    });

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN FORMS
      if (appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'You can only view imaging forms for your own appointments',
        );
      }
    }

    // DOCTORS CAN VIEW FORMS IF THEY ARE THE BOOKED DOCTOR OR HAVE SHARED EMR ACCESS
    if (user.userType === UserType.DOCTOR) {
      const isBookedDoctor = appointment.doctor.id === user.id;

      if (!isBookedDoctor) {
        const hasSharedEmrAccess =
          await this.emrService.hasDoctorSharedEmrAccess(
            user.id,
            appointment.patient.id,
          );

        if (!hasSharedEmrAccess) {
          throw new ForbiddenException(
            'Not authorized to view this imaging form',
          );
        }
      }
    }

    // FETCH IMAGING FORM WITH FULL RELATIONS
    const imagingForm = await this.imagingFormRepository.findOne({
      where: { appointment: { id: appointmentId } },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    if (!imagingForm) {
      throw new NotFoundException(
        'Imaging form not found for this appointment',
      );
    }

    return imagingForm;
  }
  async getImagingFormsByPatient(
    user: DoctorWithUserType | PatientWithUserType,
    patientId: string,
  ): Promise<ImagingForm[]> {
    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN FORMS
      if (user.id !== patientId) {
        throw new ForbiddenException(
          'You can only view your own imaging forms',
        );
      }
    }

    // DOCTORS CAN VIEW PATIENT FORMS IF THEY HAVE APPOINTMENTS WITH PATIENT OR SHARED EMR ACCESS
    if (user.userType === UserType.DOCTOR) {
      const appointment = await this.appointmentRepository.findOne({
        where: {
          doctor: { id: user.id },
          patient: { id: patientId },
        },
      });
      const hasAppointments = !!appointment;

      const hasSharedEmrAccess = await this.emrService.hasDoctorSharedEmrAccess(
        user.id,
        patientId,
      );

      if (!hasAppointments && !hasSharedEmrAccess) {
        throw new ForbiddenException(
          "Not authorized to view this patient's imaging forms",
        );
      }
    }

    const imagingForms = await this.imagingFormRepository.find({
      where: { appointment: { patient: { id: patientId } } },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    return imagingForms;
  }
}
