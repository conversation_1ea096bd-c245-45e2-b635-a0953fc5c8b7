// src/appointment/appointment.service.ts
import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, LessThan, MoreThan, ILike } from 'typeorm';
import {
  Appointment,
  AppointmentStatus,
  CancelledBy,
} from './entities/appointment.entity';
import { CreateAppointmentDto } from './dtos/create-appointment.dto';
import { FetchPatientAppointmentsDto } from './dtos/fetch-patient-appointments.dto';
import { FetchDoctorAppointmentsDto } from './dtos/fetch-doctor-appointments.dto';
import { DoctorService } from 'src/doctor/doctor.service';
import { PatientService } from 'src/patient/patient.service';
import { ConsultationService } from 'src/consultation/consultation.service';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';
import { UserType } from 'src/common/enums/userType.enum';
import { CancelAppointmentDto } from './dtos/cancel-appointment.dto';
import * as moment from 'moment';
import { ScheduleService } from 'src/schedule/schedule.service';
import { EmrService } from 'src/emr/emr.service';

@Injectable()
export class AppointmentService {
  // CONSTRUCTOR
  constructor(
    @InjectRepository(Appointment)
    private readonly appointmentRepository: Repository<Appointment>,

    @Inject(forwardRef(() => DoctorService))
    private readonly doctorService: DoctorService,

    @Inject(forwardRef(() => PatientService))
    private readonly patientService: PatientService,

    @Inject(forwardRef(() => ConsultationService))
    private readonly consultationService: ConsultationService,

    @Inject(forwardRef(() => ScheduleService))
    private readonly scheduleService: ScheduleService,

    @Inject(forwardRef(() => EmrService))
    private readonly emrService: EmrService,
  ) {}

  // SERVICES
  async bookAppointment(
    user: PatientWithUserType,
    createAppointmentDto: CreateAppointmentDto,
  ): Promise<Appointment> {
    // VERIFY PATIENT
    const patient = await this.patientService.findById(user.id);
    if (!patient) throw new BadRequestException('Invalid patient');

    // VERIFY DOCTOR
    const doctor = await this.doctorService.findById(
      createAppointmentDto.doctorId,
    );
    if (!doctor) throw new BadRequestException('Invalid doctor');

    // VERIFY CONSULTATION
    const consultation = await this.consultationService.findById(
      createAppointmentDto.consultationId,
    );
    if (!consultation) throw new BadRequestException('Invalid consultation');

    // VALIDATE APPOINTMENT TIMES - PARSE AS LOCAL TIME TO AVOID TIMEZONE CONVERSION
    const appointmentStartMoment = moment(
      createAppointmentDto.startDateTime,
      'YYYY-MM-DDTHH:mm:ss',
    );
    const appointmentEndMoment = moment(
      createAppointmentDto.endDateTime,
      'YYYY-MM-DDTHH:mm:ss',
    );

    // CHECK IF START TIME IS IN THE PAST
    const currentMoment = moment();

    if (appointmentStartMoment.isSameOrBefore(currentMoment)) {
      throw new BadRequestException(
        'Appointment cannot be booked for a past time. Please select a future time slot.',
      );
    }

    // CHECK IF APPOINTMENT TIME MATCHES A VALID SLOT
    const isValidSlot = await this.scheduleService.validateAppointmentSlot(
      doctor.id,
      appointmentStartMoment.toDate(),
      appointmentEndMoment.toDate(),
    );

    if (!isValidSlot) {
      throw new BadRequestException(
        "Appointment cannot be booked because the slot times are incorrect. Please select a valid time slot from the doctor's schedule.",
      );
    }

    // CHECK FOR OVERLAPPING APPOINTMENTS
    const overlappingAppointment = await this.isSlotBooked(
      doctor.id,
      appointmentStartMoment.toDate(),
      appointmentEndMoment.toDate(),
    );

    if (overlappingAppointment) {
      throw new BadRequestException('This time slot is already booked');
    }

    const appt = this.appointmentRepository.create({
      doctor,
      patient,
      consultation,
      startDateTime: new Date(createAppointmentDto.startDateTime),
      endDateTime: new Date(createAppointmentDto.endDateTime),
    });
    return this.appointmentRepository.save(appt);
  }
  async cancelAppointment(
    user: DoctorWithUserType | PatientWithUserType,
    cancelAppointmentDto: CancelAppointmentDto,
  ): Promise<Appointment> {
    // VERIFY APPOINTMENT
    const appt = await this.appointmentRepository.findOne({
      where: { id: cancelAppointmentDto.appointmentId },
      relations: ['doctor', 'patient'],
    });
    if (!appt) throw new BadRequestException('Appointment not found');

    // CHECK IF APPOINTMENT IS ALREADY CANCELLED OR COMPLETED
    if (appt.appointmentStatus === AppointmentStatus.CANCELLED) {
      throw new BadRequestException('Appointment is already cancelled');
    }
    if (appt.appointmentStatus === AppointmentStatus.COMPLETED) {
      throw new BadRequestException('Cannot cancel a completed appointment');
    }

    // ONLY CONCERNED DOCTOR OR PATIENT CAN CANCEL THE APPOINTMENT
    if (
      (user.userType === UserType.PATIENT && appt.patient.id !== user.id) ||
      (user.userType === UserType.DOCTOR && appt.doctor.id !== user.id)
    ) {
      throw new BadRequestException(
        'Only concerned Doctor or Patient can cancel the appointment',
      );
    }

    // 48 HOUR GAP
    const hoursBefore = moment(appt.startDateTime).diff(moment(), 'hours');
    if (hoursBefore < 48) {
      throw new BadRequestException(
        'Cancellation window has passed (must be ≥ 48 hours before start time)',
      );
    }

    appt.appointmentStatus = AppointmentStatus.CANCELLED;
    appt.cancelReason = cancelAppointmentDto.cancelReason;
    appt.cancelledBy =
      user.userType === UserType.DOCTOR
        ? CancelledBy.DOCTOR
        : CancelledBy.PATIENT;
    return this.appointmentRepository.save(appt);
  }
  async getAppointmentById(
    user: DoctorWithUserType | PatientWithUserType,
    id: string,
  ): Promise<Appointment> {
    const appt = await this.appointmentRepository.findOne({
      where: { id },
      relations: [
        'doctor',
        'doctor.profilePicture',
        'doctor.signature',
        'doctor.qualifications',
        'patient',
        'patient.profilePicture',
        'consultation',
      ],
    });
    if (!appt) throw new BadRequestException('Appointment not found');

    // PATIENTS CAN ONLY VIEW THEIR OWN APPOINTMENTS
    if (user.userType === UserType.PATIENT && appt.patient.id !== user.id) {
      throw new ForbiddenException('Not authorized to view this appointment');
    }

    // DOCTORS CAN VIEW APPOINTMENTS IF THEY ARE THE BOOKED DOCTOR OR HAVE SHARED EMR ACCESS
    if (user.userType === UserType.DOCTOR) {
      const isBookedDoctor = appt.doctor.id === user.id;

      if (!isBookedDoctor) {
        const hasSharedEmrAccess =
          await this.emrService.hasDoctorSharedEmrAccess(
            user.id,
            appt.patient.id,
          );

        if (!hasSharedEmrAccess) {
          throw new ForbiddenException(
            'Not authorized to view this appointment',
          );
        }
      }
    }

    return appt;
  }
  async getAppointmentsOfPatient(
    user: DoctorWithUserType | PatientWithUserType,
    patientId: string,
    fetchPatientAppointmentsDto?: FetchPatientAppointmentsDto,
  ): Promise<{
    appointments: Appointment[];
    total: number;
    offset: number;
    limit: number;
    totalUpcomingAppointments: number;
    totalCompletedAppointments: number;
    totalCancelledAppointments: number;
  }> {
    // PATIENTS MAY ONLY FETCH THEIR OWN
    if (user.userType === UserType.PATIENT && user.id !== patientId) {
      throw new ForbiddenException('Not authorized');
    }

    // DOCTORS CAN FETCH PATIENT APPOINTMENTS IF THEY HAVE APPOINTMENTS WITH PATIENT OR SHARED EMR ACCESS
    if (user.userType === UserType.DOCTOR) {
      const appointment = await this.appointmentRepository.findOne({
        where: {
          doctor: { id: user.id },
          patient: { id: patientId },
        },
      });
      const hasAppointments = !!appointment;

      const hasSharedEmrAccess = await this.emrService.hasDoctorSharedEmrAccess(
        user.id,
        patientId,
      );

      if (!hasAppointments && !hasSharedEmrAccess) {
        throw new ForbiddenException(
          "Not authorized to view this patient's appointments",
        );
      }
    }

    const {
      appointmentStatus,
      searchText,
      startDate,
      endDate,
      offset = 0,
      limit = 20,
    } = fetchPatientAppointmentsDto || {};

    // VALIDATE DATE RANGE
    if ((startDate && !endDate) || (!startDate && endDate)) {
      throw new BadRequestException(
        'Both startDate and endDate must be provided together',
      );
    }

    if (
      startDate &&
      endDate &&
      moment.utc(startDate).isAfter(moment.utc(endDate))
    ) {
      throw new BadRequestException(
        'startDate must be before or equal to endDate',
      );
    }

    const whereConditions: any = {
      patient: { id: patientId },
    };

    // FILTER BY APPOINTMENT STATUS
    if (appointmentStatus) {
      whereConditions.appointmentStatus = appointmentStatus;
    }

    // FILTER BY DATE RANGE
    if (startDate && endDate) {
      const startMoment = moment.utc(startDate).startOf('day');
      const endMoment = moment.utc(endDate).endOf('day');

      whereConditions.startDateTime = Between(
        startMoment.toDate(),
        endMoment.toDate(),
      );
    }

    // BUILD WHERE CONDITIONS WITH SEARCH TEXT FILTER
    let finalWhereConditions: any = whereConditions;

    if (searchText) {
      const searchPattern = `%${searchText}%`;
      finalWhereConditions = [
        {
          ...whereConditions,
          doctor: { firstName: ILike(searchPattern) },
        },
        {
          ...whereConditions,
          doctor: { lastName: ILike(searchPattern) },
        },
        // {
        //   ...whereConditions,
        //   id: Like(searchPattern),
        // },
        {
          ...whereConditions,
          consultation: { serviceName: ILike(searchPattern) },
        },
      ];
    }

    // GET APPOINTMENTS WITH PAGINATION AT DATABASE LEVEL
    const [appointments, total] = await this.appointmentRepository.findAndCount(
      {
        where: finalWhereConditions,
        relations: [
          'doctor',
          'doctor.profilePicture',
          'patient',
          'patient.profilePicture',
          'consultation',
        ],
        order: { startDateTime: 'DESC' },
        skip: offset,
        take: limit,
      },
    );

    // GET COUNTS FOR EACH STATUS - ONLY FILTER BY PATIENT (NO OTHER FILTERS)
    const [
      totalUpcomingAppointments,
      totalCompletedAppointments,
      totalCancelledAppointments,
    ] = await Promise.all([
      this.appointmentRepository.count({
        where: {
          patient: { id: patientId },
          appointmentStatus: AppointmentStatus.UPCOMING,
        },
      }),
      this.appointmentRepository.count({
        where: {
          patient: { id: patientId },
          appointmentStatus: AppointmentStatus.COMPLETED,
        },
      }),
      this.appointmentRepository.count({
        where: {
          patient: { id: patientId },
          appointmentStatus: AppointmentStatus.CANCELLED,
        },
      }),
    ]);

    return {
      appointments,
      total,
      offset,
      limit,
      totalUpcomingAppointments,
      totalCompletedAppointments,
      totalCancelledAppointments,
    };
  }
  async getAppointmentsOfDoctor(
    user: DoctorWithUserType,
    doctorId?: string,
    fetchDoctorAppointmentsDto?: FetchDoctorAppointmentsDto,
  ): Promise<{
    appointments: Appointment[];
    total: number;
    offset: number;
    limit: number;
    totalUpcomingAppointments: number;
    totalCompletedAppointments: number;
    totalCancelledAppointments: number;
  }> {
    const idToUse = doctorId ?? user.id;
    if (idToUse !== user.id) {
      throw new ForbiddenException('Not authorized');
    }

    const {
      appointmentStatus,
      searchText,
      startDate,
      endDate,
      offset = 0,
      limit = 20,
    } = fetchDoctorAppointmentsDto || {};

    // VALIDATE DATE RANGE
    if ((startDate && !endDate) || (!startDate && endDate)) {
      throw new BadRequestException(
        'Both startDate and endDate must be provided together',
      );
    }

    if (
      startDate &&
      endDate &&
      moment.utc(startDate).isAfter(moment.utc(endDate))
    ) {
      throw new BadRequestException(
        'startDate must be before or equal to endDate',
      );
    }

    const whereConditions: any = {
      doctor: { id: idToUse },
    };

    // FILTER BY APPOINTMENT STATUS
    if (appointmentStatus) {
      whereConditions.appointmentStatus = appointmentStatus;
    }

    // FILTER BY DATE RANGE
    if (startDate && endDate) {
      const startMoment = moment.utc(startDate).startOf('day');
      const endMoment = moment.utc(endDate).endOf('day');

      whereConditions.startDateTime = Between(
        startMoment.toDate(),
        endMoment.toDate(),
      );
    }

    // BUILD WHERE CONDITIONS WITH SEARCH TEXT FILTER
    let finalWhereConditions: any = whereConditions;

    if (searchText) {
      const searchPattern = `%${searchText}%`;
      finalWhereConditions = [
        {
          ...whereConditions,
          patient: { firstName: ILike(searchPattern) },
        },
        {
          ...whereConditions,
          patient: { lastName: ILike(searchPattern) },
        },
        // {
        //   ...whereConditions,
        //   id: Like(searchPattern),
        // },
        {
          ...whereConditions,
          consultation: { serviceName: ILike(searchPattern) },
        },
      ];
    }

    // GET APPOINTMENTS WITH PAGINATION AT DATABASE LEVEL
    const [appointments, total] = await this.appointmentRepository.findAndCount(
      {
        where: finalWhereConditions,
        relations: [
          'doctor',
          'doctor.profilePicture',
          'patient',
          'patient.profilePicture',
          'consultation',
        ],
        order: { startDateTime: 'DESC' },
        skip: offset,
        take: limit,
      },
    );

    // GET COUNTS FOR EACH STATUS - ONLY FILTER BY DOCTOR (NO OTHER FILTERS)
    const [
      totalUpcomingAppointments,
      totalCompletedAppointments,
      totalCancelledAppointments,
    ] = await Promise.all([
      this.appointmentRepository.count({
        where: {
          doctor: { id: idToUse },
          appointmentStatus: AppointmentStatus.UPCOMING,
        },
      }),
      this.appointmentRepository.count({
        where: {
          doctor: { id: idToUse },
          appointmentStatus: AppointmentStatus.COMPLETED,
        },
      }),
      this.appointmentRepository.count({
        where: {
          doctor: { id: idToUse },
          appointmentStatus: AppointmentStatus.CANCELLED,
        },
      }),
    ]);

    return {
      appointments,
      total,
      offset,
      limit,
      totalUpcomingAppointments,
      totalCompletedAppointments,
      totalCancelledAppointments,
    };
  }

  // HELPER FUNCTIONS
  // TELLS IF A SLOT OF A DOCTOR IS ALREADY BOOKED
  async isSlotBooked(
    doctorId: string,
    startDateTime: Date,
    endDateTime: Date,
  ): Promise<any> {
    const overlappingAppointment = await this.appointmentRepository.findOne({
      where: {
        doctor: { id: doctorId },
        appointmentStatus: AppointmentStatus.UPCOMING,
        startDateTime: LessThan(endDateTime),
        endDateTime: MoreThan(startDateTime),
      },
      relations: ['patient', 'consultation'],
    });

    return overlappingAppointment;
  }
  // GETS ALL DOCTORS WHO HAVE APPOINTMENTS WITH A PATIENT
  async getDoctorsWithPatientAppointments(patientId: string): Promise<any[]> {
    const appointments = await this.appointmentRepository.find({
      where: { patient: { id: patientId } },
      relations: ['doctor', 'doctor.profilePicture', 'doctor.qualifications'],
    });

    // DEDUPLICATE DOCTORS
    const doctorMap = new Map();
    appointments.forEach((appointment) => {
      if (!doctorMap.has(appointment.doctor.id)) {
        doctorMap.set(appointment.doctor.id, appointment.doctor);
      }
    });

    return Array.from(doctorMap.values());
  }
}
