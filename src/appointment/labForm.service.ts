// src/appointment/labForm.service.ts
import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LabForm } from './entities/labForm.entity';
import { Appointment, AppointmentStatus } from './entities/appointment.entity';
import { CreateLabFormDto } from './dtos/create-labForm.dto';
import { UpdateLabFormDto } from './dtos/update-labForm.dto';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';
import { UserType } from 'src/common/enums/userType.enum';
import { EmrService } from 'src/emr/emr.service';

@Injectable()
export class LabFormService {
  // CONSTRUCTOR
  constructor(
    @InjectRepository(LabForm)
    private readonly labFormRepository: Repository<LabForm>,
    @InjectRepository(Appointment)
    private readonly appointmentRepository: Repository<Appointment>,
    @Inject(forwardRef(() => EmrService))
    private readonly emrService: EmrService,
  ) {}

  // SERVICES
  async createLabForm(
    user: DoctorWithUserType,
    createLabFormDto: CreateLabFormDto,
  ): Promise<LabForm> {
    const { appointmentId, ...formData } = createLabFormDto;

    // FIND APPOINTMENT WITH RELATIONS
    const appointment = await this.appointmentRepository.findOne({
      where: { id: appointmentId },
      relations: [
        'patient',
        'patient.profilePicture',
        'doctor',
        'doctor.profilePicture',
        'doctor.signature',
        'labForm',
      ],
    });

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    // CHECK IF USER IS THE BOOKED DOCTOR
    if (appointment.doctor.id !== user.id) {
      throw new ForbiddenException(
        'Only the booked doctor can create lab forms',
      );
    }

    // CHECK IF APPOINTMENT STATUS IS UPCOMING OR COMPLETED
    if (
      appointment.appointmentStatus !== AppointmentStatus.UPCOMING &&
      appointment.appointmentStatus !== AppointmentStatus.COMPLETED
    ) {
      throw new BadRequestException(
        'Lab forms can only be created for upcoming or completed appointments',
      );
    }

    // CHECK IF LAB FORM ALREADY EXISTS
    if (appointment.labForm) {
      throw new BadRequestException(
        'Lab form already exists for this appointment',
      );
    }

    // CREATE FORM
    const labForm = this.labFormRepository.create({
      ...formData,
      appointment,
    });

    return await this.labFormRepository.save(labForm);
  }

  async updateLabForm(
    user: DoctorWithUserType,
    updateLabFormDto: UpdateLabFormDto,
  ): Promise<LabForm> {
    const { id, appointmentId, ...updateData } = updateLabFormDto;

    // VALIDATE THAT EITHER ID OR APPOINTMENTID IS PROVIDED
    if (!id && !appointmentId) {
      throw new BadRequestException(
        'Either form id or appointmentId must be provided',
      );
    }

    let labForm: LabForm;

    if (id) {
      // FIND BY FORM ID
      labForm = await this.labFormRepository.findOne({
        where: { id },
        relations: [
          'appointment',
          'appointment.patient',
          'appointment.patient.profilePicture',
          'appointment.doctor',
          'appointment.doctor.profilePicture',
          'appointment.doctor.signature',
        ],
      });
    } else {
      // FIND BY APPOINTMENT ID
      labForm = await this.labFormRepository.findOne({
        where: { appointment: { id: appointmentId } },
        relations: [
          'appointment',
          'appointment.patient',
          'appointment.patient.profilePicture',
          'appointment.doctor',
          'appointment.doctor.profilePicture',
          'appointment.doctor.signature',
        ],
      });
    }

    if (!labForm) {
      throw new NotFoundException('Lab form not found');
    }

    // CHECK IF USER IS THE BOOKED DOCTOR
    if (labForm.appointment.doctor.id !== user.id) {
      throw new ForbiddenException('Only the booked doctor can edit lab forms');
    }

    // CHECK IF APPOINTMENT STATUS IS UPCOMING OR COMPLETED
    if (
      labForm.appointment.appointmentStatus !== AppointmentStatus.UPCOMING &&
      labForm.appointment.appointmentStatus !== AppointmentStatus.COMPLETED
    ) {
      throw new BadRequestException(
        'Lab forms can only be edited for upcoming or completed appointments',
      );
    }

    // UPDATE FORM
    Object.assign(labForm, updateData);
    return await this.labFormRepository.save(labForm);
  }

  async getLabFormById(
    user: DoctorWithUserType | PatientWithUserType,
    id: string,
  ): Promise<LabForm> {
    // FIND FORM WITH RELATIONS
    const labForm = await this.labFormRepository.findOne({
      where: { id },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    if (!labForm) {
      throw new NotFoundException('Lab form not found');
    }

    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN FORMS
      if (labForm.appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'You can only view lab forms for your own appointments',
        );
      }
    }

    if (user.userType === UserType.DOCTOR) {
      // CHECK IF DOCTOR IS THE BOOKED DOCTOR
      const isBookedDoctor = labForm.appointment.doctor.id === user.id;

      if (!isBookedDoctor) {
        // CHECK IF DOCTOR HAS SHARED EMR ACCESS TO THE PATIENT
        const hasSharedEmrAccess =
          await this.emrService.hasDoctorSharedEmrAccess(
            user.id,
            labForm.appointment.patient.id,
          );

        if (!hasSharedEmrAccess) {
          throw new ForbiddenException('Not authorized to view this lab form');
        }
      }
    }

    return labForm;
  }

  async getLabFormByAppointmentId(
    user: DoctorWithUserType | PatientWithUserType,
    appointmentId: string,
  ): Promise<LabForm> {
    // FIND APPOINTMENT FOR AUTHORIZATION CHECK
    const appointment = await this.appointmentRepository.findOne({
      where: { id: appointmentId },
      relations: ['patient', 'doctor'],
    });

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN FORMS
      if (appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'You can only view lab forms for your own appointments',
        );
      }
    }

    if (user.userType === UserType.DOCTOR) {
      // CHECK IF DOCTOR IS THE BOOKED DOCTOR
      const isBookedDoctor = appointment.doctor.id === user.id;

      if (!isBookedDoctor) {
        // CHECK IF DOCTOR HAS SHARED EMR ACCESS TO THE PATIENT
        const hasSharedEmrAccess =
          await this.emrService.hasDoctorSharedEmrAccess(
            user.id,
            appointment.patient.id,
          );

        if (!hasSharedEmrAccess) {
          throw new ForbiddenException('Not authorized to view this lab form');
        }
      }
    }

    // FETCH LAB FORM WITH FULL RELATIONS
    const labForm = await this.labFormRepository.findOne({
      where: { appointment: { id: appointmentId } },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    if (!labForm) {
      throw new NotFoundException('Lab form not found for this appointment');
    }

    return labForm;
  }

  async getLabFormsByPatientId(
    user: DoctorWithUserType | PatientWithUserType,
    patientId: string,
  ): Promise<LabForm[]> {
    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN FORMS
      if (user.id !== patientId) {
        throw new ForbiddenException('You can only view your own lab forms');
      }
    }

    // DOCTORS CAN VIEW PATIENT FORMS IF THEY HAVE APPOINTMENTS WITH PATIENT OR SHARED EMR ACCESS
    if (user.userType === UserType.DOCTOR) {
      const appointment = await this.appointmentRepository.findOne({
        where: {
          doctor: { id: user.id },
          patient: { id: patientId },
        },
      });
      const hasAppointments = !!appointment;

      const hasSharedEmrAccess = await this.emrService.hasDoctorSharedEmrAccess(
        user.id,
        patientId,
      );

      if (!hasAppointments && !hasSharedEmrAccess) {
        throw new ForbiddenException(
          "Not authorized to view this patient's lab forms",
        );
      }
    }

    const labForms = await this.labFormRepository.find({
      where: { appointment: { patient: { id: patientId } } },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    return labForms;
  }
}
