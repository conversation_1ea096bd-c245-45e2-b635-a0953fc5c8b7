// src/appointment/prescriptionForm.controller.ts
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { CreatePrescriptionFormDto } from './dtos/create-prescriptionForm.dto';
import { UpdatePrescriptionFormDto } from './dtos/update-prescriptionForm.dto';
import { FetchPatientPrescriptionsDto } from './dtos/fetch-patient-prescriptions.dto';
import { AuthenticationGuard } from 'src/auth/guards/authentication.guard';
import { AuthorizationGuard } from 'src/auth/guards/authorization.guard';
import { UserTypes } from 'src/common/decorators/userType.decorator';
import { UserType } from 'src/common/enums/userType.enum';
import { RequestWithUser } from 'src/common/interfaces/requestWithUser.interface';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';
import { PrescriptionFormService } from './prescriptionForm.service';

@Controller('api/prescriptionForm')
export class PrescriptionFormController {
  // CONSTRUCTOR
  constructor(
    private readonly prescriptionFormService: PrescriptionFormService,
  ) {}

  // CONTROLLERS
  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Post('create')
  async createPrescriptionForm(
    @Body() createPrescriptionFormDto: CreatePrescriptionFormDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.prescriptionFormService.createPrescriptionForm(
      user,
      createPrescriptionFormDto,
    );
    return { message: 'Prescription form created successfully', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Put('update')
  async updatePrescriptionForm(
    @Body() updatePrescriptionFormDto: UpdatePrescriptionFormDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.prescriptionFormService.updatePrescriptionForm(
      user,
      updatePrescriptionFormDto,
    );
    return { message: 'Prescription form updated successfully', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Delete('delete/:id')
  async deletePrescriptionForm(
    @Param('id') id: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    await this.prescriptionFormService.deletePrescriptionForm(user, id);
    return { message: 'Prescription form deleted successfully' };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getById/:id')
  async getPrescriptionFormById(
    @Param('id') id: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.prescriptionFormService.getPrescriptionFormById(
      user,
      id,
    );
    return { message: 'Prescription form fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getByAppointmentId/:appointmentId')
  async getPrescriptionFormsByAppointmentId(
    @Param('appointmentId') appointmentId: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data =
      await this.prescriptionFormService.getPrescriptionFormsByAppointmentId(
        user,
        appointmentId,
      );
    return { message: 'Prescription forms fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getByPatientId/:patientId')
  async getPrescriptionFormsByPatient(
    @Param('patientId') patientId: string,
    @Query() fetchPatientPrescriptionsDto: FetchPatientPrescriptionsDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data =
      await this.prescriptionFormService.getPrescriptionFormsByPatient(
        user,
        patientId,
        fetchPatientPrescriptionsDto,
      );
    return { message: 'Prescription forms fetched successfully', data };
  }
}
