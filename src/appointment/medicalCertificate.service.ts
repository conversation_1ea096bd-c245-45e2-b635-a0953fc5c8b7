// src/appointment/medicalCertificate.service.ts
import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MedicalCertificate } from './entities/medicalCertificate.entity';
import { Appointment, AppointmentStatus } from './entities/appointment.entity';
import { CreateMedicalCertificateDto } from './dtos/create-medicalCertificate.dto';
import { UpdateMedicalCertificateDto } from './dtos/update-medicalCertificate.dto';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';
import { UserType } from 'src/common/enums/userType.enum';
import { EmrService } from 'src/emr/emr.service';

@Injectable()
export class MedicalCertificateService {
  // CONSTRUCTOR
  constructor(
    @InjectRepository(MedicalCertificate)
    private readonly medicalCertificateRepository: Repository<MedicalCertificate>,
    @InjectRepository(Appointment)
    private readonly appointmentRepository: Repository<Appointment>,
    @Inject(forwardRef(() => EmrService))
    private readonly emrService: EmrService,
  ) {}

  // SERVICES
  async createMedicalCertificate(
    user: DoctorWithUserType,
    createMedicalCertificateDto: CreateMedicalCertificateDto,
  ): Promise<MedicalCertificate> {
    const { appointmentId, ...formData } = createMedicalCertificateDto;

    // FIND APPOINTMENT WITH RELATIONS
    const appointment = await this.appointmentRepository.findOne({
      where: { id: appointmentId },
      relations: [
        'patient',
        'patient.profilePicture',
        'doctor',
        'doctor.profilePicture',
        'doctor.signature',
        'medicalCertificate',
      ],
    });

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    // CHECK IF USER IS THE BOOKED DOCTOR
    if (appointment.doctor.id !== user.id) {
      throw new ForbiddenException(
        'Only the booked doctor can create medical certificates',
      );
    }

    // CHECK IF APPOINTMENT STATUS IS UPCOMING OR COMPLETED
    if (
      appointment.appointmentStatus !== AppointmentStatus.UPCOMING &&
      appointment.appointmentStatus !== AppointmentStatus.COMPLETED
    ) {
      throw new BadRequestException(
        'Medical certificates can only be created for upcoming or completed appointments',
      );
    }

    // CHECK IF MEDICAL CERTIFICATE ALREADY EXISTS
    if (appointment.medicalCertificate) {
      throw new BadRequestException(
        'Medical certificate already exists for this appointment',
      );
    }

    // CREATE FORM
    const medicalCertificate = this.medicalCertificateRepository.create({
      ...formData,
      appointment,
    });

    return await this.medicalCertificateRepository.save(medicalCertificate);
  }

  async updateMedicalCertificate(
    user: DoctorWithUserType,
    updateMedicalCertificateDto: UpdateMedicalCertificateDto,
  ): Promise<MedicalCertificate> {
    const { id, appointmentId, ...updateData } = updateMedicalCertificateDto;

    // VALIDATE THAT EITHER ID OR APPOINTMENTID IS PROVIDED
    if (!id && !appointmentId) {
      throw new BadRequestException(
        'Either form id or appointmentId must be provided',
      );
    }

    let medicalCertificate: MedicalCertificate;

    if (id) {
      // FIND BY FORM ID
      medicalCertificate = await this.medicalCertificateRepository.findOne({
        where: { id },
        relations: [
          'appointment',
          'appointment.patient',
          'appointment.patient.profilePicture',
          'appointment.doctor',
          'appointment.doctor.profilePicture',
          'appointment.doctor.signature',
        ],
      });
    } else {
      // FIND BY APPOINTMENT ID
      medicalCertificate = await this.medicalCertificateRepository.findOne({
        where: { appointment: { id: appointmentId } },
        relations: [
          'appointment',
          'appointment.patient',
          'appointment.patient.profilePicture',
          'appointment.doctor',
          'appointment.doctor.profilePicture',
          'appointment.doctor.signature',
        ],
      });
    }

    if (!medicalCertificate) {
      throw new NotFoundException('Medical certificate not found');
    }

    // CHECK IF USER IS THE BOOKED DOCTOR
    if (medicalCertificate.appointment.doctor.id !== user.id) {
      throw new ForbiddenException(
        'Only the booked doctor can edit medical certificates',
      );
    }

    // CHECK IF APPOINTMENT STATUS IS UPCOMING OR COMPLETED
    if (
      medicalCertificate.appointment.appointmentStatus !==
        AppointmentStatus.UPCOMING &&
      medicalCertificate.appointment.appointmentStatus !==
        AppointmentStatus.COMPLETED
    ) {
      throw new BadRequestException(
        'Medical certificates can only be edited for upcoming or completed appointments',
      );
    }

    // UPDATE FORM
    Object.assign(medicalCertificate, updateData);
    return await this.medicalCertificateRepository.save(medicalCertificate);
  }

  async getMedicalCertificateById(
    user: DoctorWithUserType | PatientWithUserType,
    id: string,
  ): Promise<MedicalCertificate> {
    // FIND FORM WITH RELATIONS
    const medicalCertificate = await this.medicalCertificateRepository.findOne({
      where: { id },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    if (!medicalCertificate) {
      throw new NotFoundException('Medical certificate not found');
    }

    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN FORMS
      if (medicalCertificate.appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'You can only view medical certificates for your own appointments',
        );
      }
    }

    if (user.userType === UserType.DOCTOR) {
      // DOCTOR CAN VIEW IF THEY ARE THE BOOKED DOCTOR OR HAVE SHARED EMR ACCESS
      const isBookedDoctor =
        medicalCertificate.appointment.doctor.id === user.id;

      if (!isBookedDoctor) {
        const hasSharedEmrAccess =
          await this.emrService.hasDoctorSharedEmrAccess(
            user.id,
            medicalCertificate.appointment.patient.id,
          );

        if (!hasSharedEmrAccess) {
          throw new ForbiddenException(
            'Not authorized to view this medical certificate',
          );
        }
      }
    }

    return medicalCertificate;
  }

  async getMedicalCertificateByAppointmentId(
    user: DoctorWithUserType | PatientWithUserType,
    appointmentId: string,
  ): Promise<MedicalCertificate> {
    // FIND APPOINTMENT FOR AUTHORIZATION CHECK
    const appointment = await this.appointmentRepository.findOne({
      where: { id: appointmentId },
      relations: ['patient', 'doctor'],
    });

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN FORMS
      if (appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'You can only view medical certificates for your own appointments',
        );
      }
    }

    if (user.userType === UserType.DOCTOR) {
      // DOCTOR CAN VIEW IF THEY ARE THE BOOKED DOCTOR OR HAVE SHARED EMR ACCESS
      const isBookedDoctor = appointment.doctor.id === user.id;

      if (!isBookedDoctor) {
        const hasSharedEmrAccess =
          await this.emrService.hasDoctorSharedEmrAccess(
            user.id,
            appointment.patient.id,
          );

        if (!hasSharedEmrAccess) {
          throw new ForbiddenException(
            'Not authorized to view this medical certificate',
          );
        }
      }
    }

    // FETCH MEDICAL CERTIFICATE WITH FULL RELATIONS
    const medicalCertificate = await this.medicalCertificateRepository.findOne({
      where: { appointment: { id: appointmentId } },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    if (!medicalCertificate) {
      throw new NotFoundException(
        'Medical certificate not found for this appointment',
      );
    }

    return medicalCertificate;
  }

  async getMedicalCertificatesByPatient(
    user: DoctorWithUserType | PatientWithUserType,
    patientId: string,
  ): Promise<MedicalCertificate[]> {
    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN FORMS
      if (user.id !== patientId) {
        throw new ForbiddenException(
          'You can only view your own medical certificates',
        );
      }
    }

    if (user.userType === UserType.DOCTOR) {
      // DOCTOR CAN VIEW PATIENT FORMS IF THEY HAVE APPOINTMENTS WITH PATIENT OR SHARED EMR ACCESS
      const appointment = await this.appointmentRepository.findOne({
        where: {
          doctor: { id: user.id },
          patient: { id: patientId },
        },
      });
      const hasAppointments = !!appointment;

      const hasSharedEmrAccess = await this.emrService.hasDoctorSharedEmrAccess(
        user.id,
        patientId,
      );

      if (!hasAppointments && !hasSharedEmrAccess) {
        throw new ForbiddenException(
          "Not authorized to view this patient's medical certificates",
        );
      }
    }

    // FETCH MEDICAL CERTIFICATES
    const medicalCertificates = await this.medicalCertificateRepository.find({
      where: { appointment: { patient: { id: patientId } } },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
      order: { createdAt: 'DESC' },
    });

    return medicalCertificates;
  }
}
