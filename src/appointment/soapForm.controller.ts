// src/appointment/soapForm.controller.ts
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { SoapFormService } from './soapForm.service';
import { CreateSoapFormDto } from './dtos/create-soapForm.dto';
import { UpdateSoapFormDto } from './dtos/update-soapForm.dto';
import { AuthenticationGuard } from 'src/auth/guards/authentication.guard';
import { AuthorizationGuard } from 'src/auth/guards/authorization.guard';
import { UserTypes } from 'src/common/decorators/userType.decorator';
import { UserType } from 'src/common/enums/userType.enum';
import { RequestWithUser } from 'src/common/interfaces/requestWithUser.interface';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';

@Controller('api/soapForm')
export class SoapFormController {
  // CONSTRUCTOR
  constructor(private readonly soapFormService: SoapFormService) {}

  // CONTROLLERS
  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Post('create')
  async createSoapForm(
    @Body() createSoapFormDto: CreateSoapFormDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.soapFormService.createSoapForm(
      user,
      createSoapFormDto,
    );
    return { message: 'SOAP form created successfully', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Put('update')
  async updateSoapForm(
    @Body() updateSoapFormDto: UpdateSoapFormDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.soapFormService.updateSoapForm(
      user,
      updateSoapFormDto,
    );
    return { message: 'SOAP form updated successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getById/:id')
  async getSoapFormById(
    @Param('id') id: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.soapFormService.getSoapFormById(user, id);
    return { message: 'SOAP form fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getByAppointmentId/:appointmentId')
  async getSoapFormByAppointmentId(
    @Param('appointmentId') appointmentId: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.soapFormService.getSoapFormByAppointmentId(
      user,
      appointmentId,
    );
    return { message: 'SOAP form fetched successfully', data };
  }
}
