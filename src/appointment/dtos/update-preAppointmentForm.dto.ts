// src/appointment/dtos/update-preAppointmentForm.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { CreatePreAppointmentFormDto } from './create-preAppointmentForm.dto';
import { IsOptional, IsUUID } from 'class-validator';

export class UpdatePreAppointmentFormDto extends PartialType(
  CreatePreAppointmentFormDto,
) {
  @IsOptional()
  @IsUUID()
  id?: string;

  @IsOptional()
  @IsUUID()
  appointmentId?: string;
}
