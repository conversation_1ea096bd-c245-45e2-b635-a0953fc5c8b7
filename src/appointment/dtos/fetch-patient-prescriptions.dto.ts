// src/appointment/dtos/fetch-patient-prescriptions.dto.ts
import { Type } from 'class-transformer';
import {
  IsNumber,
  IsOptional,
  IsString,
  IsDateString,
  ValidateIf,
} from 'class-validator';

export class FetchPatientPrescriptionsDto {
  @IsOptional()
  @IsString()
  searchText?: string;

  @IsOptional()
  @IsDateString()
  @ValidateIf((o) => o.endDate !== undefined)
  startDate?: string;

  @IsOptional()
  @IsDateString()
  @ValidateIf((o) => o.startDate !== undefined)
  endDate?: string;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  offset?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number;
}
