// src/appointment/dtos/create-rosForm.dto.ts
import {
  IsOptional,
  IsString,
  IsUUID,
  IsNotEmpty,
  IsDateString,
} from 'class-validator';
import { IsOptionalBoolean } from 'src/common/decorators/isOptionalBoolean.decorator';

export class CreateRosFormDto {
  @IsUUID()
  @IsNotEmpty()
  appointmentId: string;

  // GENERAL
  @IsOptionalBoolean()
  recentWeightChange?: boolean | null;

  @IsOptionalBoolean()
  weakness?: boolean | null;

  @IsOptionalBoolean()
  fatigue?: boolean | null;

  @IsOptionalBoolean()
  fever?: boolean | null;

  // SKIN
  @IsOptionalBoolean()
  skinRashes?: boolean | null;

  @IsOptionalBoolean()
  skinLumps?: boolean | null;

  @IsOptionalBoolean()
  skinSores?: boolean | null;

  @IsOptionalBoolean()
  skinItching?: boolean | null;

  @IsOptionalBoolean()
  skinDryness?: boolean | null;

  @IsOptionalBoolean()
  skinChangeInColor?: boolean | null;

  @IsOptionalBoolean()
  skinChangeInHair?: boolean | null;

  @IsOptionalBoolean()
  skinChangeInMoles?: boolean | null;

  // HEENT - Head
  @IsOptionalBoolean()
  headache?: boolean | null;

  @IsOptionalBoolean()
  headInjury?: boolean | null;

  @IsOptionalBoolean()
  dizziness?: boolean | null;

  @IsOptionalBoolean()
  lightheadedness?: boolean | null;

  // HEENT - Eyes
  @IsOptionalBoolean()
  visionChange?: boolean | null;

  @IsOptionalBoolean()
  glassesOrContactLenses?: boolean | null;

  @IsOptionalBoolean()
  eyePain?: boolean | null;

  @IsOptionalBoolean()
  eyeRedness?: boolean | null;

  @IsOptionalBoolean()
  excessiveTearing?: boolean | null;

  @IsOptionalBoolean()
  doubleOrBlurredVision?: boolean | null;

  @IsOptionalBoolean()
  spots?: boolean | null;

  @IsOptionalBoolean()
  specks?: boolean | null;

  @IsOptionalBoolean()
  flashingLights?: boolean | null;

  @IsOptionalBoolean()
  glaucoma?: boolean | null;

  @IsOptionalBoolean()
  cataracts?: boolean | null;

  // HEENT - Ears
  @IsOptionalBoolean()
  hearingLoss?: boolean | null;

  @IsOptionalBoolean()
  tinnitus?: boolean | null;

  @IsOptionalBoolean()
  vertigo?: boolean | null;

  @IsOptionalBoolean()
  earaches?: boolean | null;

  @IsOptionalBoolean()
  earInfection?: boolean | null;

  @IsOptionalBoolean()
  earDischarge?: boolean | null;

  @IsOptionalBoolean()
  hearingDecreased?: boolean | null;

  @IsOptionalBoolean()
  hearingAidsUse?: boolean | null;

  // HEENT - Nose and Sinuses
  @IsOptionalBoolean()
  nasalDischarge?: boolean | null;

  @IsOptionalBoolean()
  nasalItching?: boolean | null;

  @IsOptionalBoolean()
  frequentColds?: boolean | null;

  @IsOptionalBoolean()
  hayfever?: boolean | null;

  @IsOptionalBoolean()
  nasalStuffiness?: boolean | null;

  @IsOptionalBoolean()
  nosebleeds?: boolean | null;

  @IsOptionalBoolean()
  sinusPressurePain?: boolean | null;

  @IsOptionalBoolean()
  dentalCondition?: boolean | null;

  @IsOptionalBoolean()
  gumProblemsBleeding?: boolean | null;

  @IsOptionalBoolean()
  dentures?: boolean | null;

  @IsOptional()
  @IsString()
  denturesFit?: string;

  @IsOptional()
  @IsDateString()
  lastDentalExam?: Date;

  @IsOptionalBoolean()
  soreTongue?: boolean | null;

  @IsOptionalBoolean()
  dryMouth?: boolean | null;

  @IsOptionalBoolean()
  frequentSoreThroats?: boolean | null;

  @IsOptionalBoolean()
  hoarseness?: boolean | null;

  // NECK
  @IsOptionalBoolean()
  swollenGlands?: boolean | null;

  @IsOptionalBoolean()
  thyroidProblems?: boolean | null;

  @IsOptionalBoolean()
  goiter?: boolean | null;

  @IsOptionalBoolean()
  neckLumps?: boolean | null;

  @IsOptionalBoolean()
  neckPainStiffness?: boolean | null;

  // BREASTS
  @IsOptionalBoolean()
  breastLumps?: boolean | null;

  @IsOptionalBoolean()
  breastPainDiscomfort?: boolean | null;

  @IsOptionalBoolean()
  nippleDischarge?: boolean | null;

  @IsOptionalBoolean()
  selfExamPractices?: boolean | null;

  // RESPIRATORY
  @IsOptionalBoolean()
  cough?: boolean | null;

  @IsOptionalBoolean()
  sputum?: boolean | null;

  @IsOptional()
  @IsString()
  sputumColor?: string;

  @IsOptional()
  @IsString()
  sputumQuantity?: string;

  @IsOptionalBoolean()
  sputumBlood?: boolean | null;

  @IsOptionalBoolean()
  shortnessOfBreath_respiratory?: boolean | null;

  @IsOptionalBoolean()
  wheezing?: boolean | null;

  @IsOptionalBoolean()
  pleuriticPain?: boolean | null;

  @IsOptional()
  @IsDateString()
  lastChestXray?: Date;

  @IsOptionalBoolean()
  asthma?: boolean | null;

  @IsOptionalBoolean()
  bronchitis?: boolean | null;

  @IsOptionalBoolean()
  emphysema?: boolean | null;

  @IsOptionalBoolean()
  pneumonia?: boolean | null;

  @IsOptionalBoolean()
  tuberculosis?: boolean | null;

  // CARDIOVASCULAR
  @IsOptionalBoolean()
  heartTrouble?: boolean | null;

  @IsOptionalBoolean()
  highBloodPressure?: boolean | null;

  @IsOptionalBoolean()
  rheumaticFever?: boolean | null;

  @IsOptionalBoolean()
  heartMurmurs?: boolean | null;

  @IsOptionalBoolean()
  chestPain?: boolean | null;

  @IsOptionalBoolean()
  palpitations?: boolean | null;

  @IsOptionalBoolean()
  shortnessOfBreath_cardio?: boolean | null;

  @IsOptionalBoolean()
  orthopnea?: boolean | null;

  @IsOptionalBoolean()
  paroxysmalNocturnalDyspnea?: boolean | null;

  @IsOptionalBoolean()
  edema?: boolean | null;

  @IsOptional()
  @IsString()
  ekgOther?: string;

  // GASTROINTESTINAL
  @IsOptionalBoolean()
  unintentionalWeightChange?: boolean | null;

  @IsOptionalBoolean()
  troubleSwallowing?: boolean | null;

  @IsOptionalBoolean()
  heartburn?: boolean | null;

  @IsOptionalBoolean()
  appetite?: boolean | null;

  @IsOptionalBoolean()
  specialDiet?: boolean | null;

  @IsOptionalBoolean()
  nausea?: boolean | null;

  @IsOptional()
  @IsString()
  stoolColor?: string;

  @IsOptional()
  @IsString()
  stoolSize?: string;

  @IsOptionalBoolean()
  changeInBowelHabits?: boolean | null;

  @IsOptionalBoolean()
  painWithDefecation?: boolean | null;

  @IsOptionalBoolean()
  rectalBleeding?: boolean | null;

  @IsOptionalBoolean()
  blackOrTarryStools?: boolean | null;

  @IsOptionalBoolean()
  hemorrhoids?: boolean | null;

  @IsOptionalBoolean()
  constipation?: boolean | null;

  @IsOptionalBoolean()
  diarrhea?: boolean | null;

  @IsOptionalBoolean()
  abdominalPain?: boolean | null;

  @IsOptionalBoolean()
  foodIntolerance?: boolean | null;

  @IsOptionalBoolean()
  excessiveBelching?: boolean | null;

  @IsOptionalBoolean()
  jaundice?: boolean | null;

  @IsOptionalBoolean()
  liverGallbladderTrouble?: boolean | null;

  @IsOptionalBoolean()
  hepatitis?: boolean | null;

  // PERIPHERAL VASCULAR
  @IsOptionalBoolean()
  claudication?: boolean | null;

  @IsOptionalBoolean()
  legCramps?: boolean | null;

  @IsOptionalBoolean()
  varicoseVeins?: boolean | null;

  @IsOptionalBoolean()
  pastClots?: boolean | null;

  @IsOptionalBoolean()
  calfLegFeetSwelling?: boolean | null;

  @IsOptionalBoolean()
  fingertipColorChangeCold?: boolean | null;

  @IsOptionalBoolean()
  swellingRednessTendernessPeripheral?: boolean | null;

  // URINARY
  @IsOptionalBoolean()
  frequencyOfUrination?: boolean | null;

  @IsOptionalBoolean()
  polyuria?: boolean | null;

  @IsOptionalBoolean()
  nocturia?: boolean | null;

  @IsOptionalBoolean()
  urgency?: boolean | null;

  @IsOptionalBoolean()
  burningPainDuringUrination?: boolean | null;

  @IsOptionalBoolean()
  hematuria?: boolean | null;

  @IsOptionalBoolean()
  urinaryInfections?: boolean | null;

  @IsOptionalBoolean()
  kidneyOrFlankPain?: boolean | null;

  @IsOptionalBoolean()
  kidneyStones?: boolean | null;

  @IsOptionalBoolean()
  ureteralColic?: boolean | null;

  @IsOptionalBoolean()
  suprapubicPain?: boolean | null;

  @IsOptionalBoolean()
  incontinence?: boolean | null;

  @IsOptionalBoolean()
  reducedUrinaryCaliberOrForce?: boolean | null;

  @IsOptionalBoolean()
  hesitancy?: boolean | null;

  @IsOptionalBoolean()
  dribbling?: boolean | null;

  // GENITAL - MALE
  @IsOptionalBoolean()
  hernias?: boolean | null;

  @IsOptionalBoolean()
  penileDischarge?: boolean | null;

  @IsOptionalBoolean()
  testicularPainOrMasses?: boolean | null;

  @IsOptionalBoolean()
  scrotalPainOrSwelling?: boolean | null;

  // History of Sexually Transmitted Infections
  @IsOptionalBoolean()
  stdHistory?: boolean | null;

  @IsOptional()
  @IsString()
  stdTreatment?: string;

  // Sexual Habits
  @IsOptionalBoolean()
  sexualInterest?: boolean | null;

  @IsOptionalBoolean()
  sexualFunction?: boolean | null;

  @IsOptionalBoolean()
  sexualSatisfaction?: boolean | null;

  @IsOptionalBoolean()
  birthControlMethodUse?: boolean | null;

  @IsOptionalBoolean()
  condomUse?: boolean | null;

  @IsOptionalBoolean()
  sexualProblems?: boolean | null;

  @IsOptionalBoolean()
  hivInfectionConcerns?: boolean | null;

  // GENITAL - FEMALE
  @IsOptional()
  @IsString()
  ageAtMenarche?: string;

  @IsOptional()
  @IsString()
  periodFrequency?: string;

  @IsOptional()
  @IsString()
  durationOfPeriods?: string;

  @IsOptional()
  @IsString()
  amountOfBleeding?: string;

  @IsOptionalBoolean()
  periodRegularity?: boolean | null;

  @IsOptionalBoolean()
  bleedingBetweenPeriods?: boolean | null;

  @IsOptionalBoolean()
  bleedingAfterIntercourse?: boolean | null;

  @IsOptional()
  @IsDateString()
  lastMenstrualPeriod?: Date;

  @IsOptionalBoolean()
  dysmenorrhea?: boolean | null;

  @IsOptionalBoolean()
  premenstrualTension?: boolean | null;

  @IsOptional()
  @IsString()
  ageAtMenopause?: string;

  @IsOptionalBoolean()
  menopausalSymptoms?: boolean | null;

  @IsOptionalBoolean()
  postMenopausalBleeding?: boolean | null;

  @IsOptionalBoolean()
  vaginalDischarge?: boolean | null;

  @IsOptionalBoolean()
  itching_female?: boolean | null;

  @IsOptionalBoolean()
  sores_female?: boolean | null;

  @IsOptionalBoolean()
  lumps_female?: boolean | null;

  @IsOptionalBoolean()
  historyOfSexuallyTransmittedInfections_female?: boolean | null;

  @IsOptional()
  @IsString()
  treatmentOfSexuallyTransmittedInfections_female?: string;

  @IsOptional()
  @IsString()
  numberOfPregnancies?: string;

  @IsOptional()
  @IsString()
  numberOfDeliveries?: string;

  @IsOptional()
  @IsString()
  typeOfDeliveries?: string;

  @IsOptional()
  @IsString()
  numberOfAbortions?: string;

  @IsOptional()
  @IsString()
  birthControlMethods?: string;

  @IsOptionalBoolean()
  complicationsOfPregnancy?: boolean | null;

  // SEXUAL HABITS (FEMALE-SPECIFIC)
  @IsOptionalBoolean()
  sexualInterest_female?: boolean | null;

  @IsOptionalBoolean()
  sexualFunction_female?: boolean | null;

  @IsOptionalBoolean()
  sexualSatisfaction_female?: boolean | null;

  @IsOptionalBoolean()
  anyProblemsIncludingDyspareunia_female?: boolean | null;

  @IsOptionalBoolean()
  concernsAboutHivInfection_female?: boolean | null;

  // MUSCULOSKELETAL
  @IsOptionalBoolean()
  muscleOrJointPain?: boolean | null;

  @IsOptionalBoolean()
  stiffness?: boolean | null;

  @IsOptionalBoolean()
  arthritis?: boolean | null;

  @IsOptionalBoolean()
  gout?: boolean | null;

  @IsOptionalBoolean()
  backache?: boolean | null;

  @IsOptional()
  @IsString()
  muscleLocationDescription?: string;

  @IsOptionalBoolean()
  swelling_muscle?: boolean | null;

  @IsOptionalBoolean()
  redness_muscle?: boolean | null;

  @IsOptionalBoolean()
  painMusculoskeletal?: boolean | null;

  @IsOptionalBoolean()
  tenderness_muscle?: boolean | null;

  @IsOptionalBoolean()
  weaknessMusculoskeletal?: boolean | null;

  @IsOptionalBoolean()
  numbnessInLimb?: boolean | null;

  @IsOptionalBoolean()
  limitationOfMotionOrActivity?: boolean | null;

  @IsOptionalBoolean()
  timingMorning?: boolean | null;

  @IsOptionalBoolean()
  timingEvening?: boolean | null;

  @IsOptional()
  @IsString()
  duration?: string;

  @IsOptionalBoolean()
  historyOfTrauma?: boolean | null;

  @IsOptionalBoolean()
  neckOrLowBackPain?: boolean | null;

  @IsOptionalBoolean()
  systemicJointPain?: boolean | null;

  // PSYCHIATRIC
  @IsOptionalBoolean()
  nervousness?: boolean | null;

  @IsOptionalBoolean()
  tension?: boolean | null;

  @IsOptionalBoolean()
  feelingDownSadDepressed?: boolean | null;

  @IsOptionalBoolean()
  memoryChange?: boolean | null;

  @IsOptionalBoolean()
  suicidePlansOrAttempts?: boolean | null;

  @IsOptionalBoolean()
  suicidalThoughts?: boolean | null;

  @IsOptionalBoolean()
  pastCounseling?: boolean | null;

  @IsOptionalBoolean()
  psychiatricAdmissions?: boolean | null;

  @IsOptionalBoolean()
  onPsychiatricMedications?: boolean | null;

  // NEUROLOGIC
  @IsOptionalBoolean()
  changeInMoodNeurologic?: boolean | null;

  @IsOptionalBoolean()
  changeInAttention?: boolean | null;

  @IsOptionalBoolean()
  changeInSpeech?: boolean | null;

  @IsOptionalBoolean()
  changeInOrientation?: boolean | null;

  @IsOptionalBoolean()
  changeInMemoryNeurologic?: boolean | null;

  @IsOptionalBoolean()
  changeInInsight?: boolean | null;

  @IsOptionalBoolean()
  changeInJudgement?: boolean | null;

  @IsOptionalBoolean()
  headacheNeurologic?: boolean | null;

  @IsOptionalBoolean()
  dizzinessNeurologic?: boolean | null;

  @IsOptionalBoolean()
  vertigoNeurologic?: boolean | null;

  @IsOptionalBoolean()
  fainting?: boolean | null;

  @IsOptionalBoolean()
  blackouts?: boolean | null;

  @IsOptionalBoolean()
  weaknessNeurologic?: boolean | null;

  @IsOptionalBoolean()
  paralysis?: boolean | null;

  @IsOptionalBoolean()
  numbnessOrLossOfSensation?: boolean | null;

  @IsOptionalBoolean()
  tinglingPinsAndNeedles?: boolean | null;

  @IsOptionalBoolean()
  tremorsOrOtherInvoluntaryMovement?: boolean | null;

  @IsOptionalBoolean()
  seizures?: boolean | null;

  // HEMATOLOGIC
  @IsOptionalBoolean()
  anemia?: boolean | null;

  @IsOptionalBoolean()
  easyBruisingOrBleeding?: boolean | null;

  @IsOptionalBoolean()
  pastTransfusions?: boolean | null;

  @IsOptionalBoolean()
  transfusionReactions?: boolean | null;

  // ENDOCRINE
  @IsOptionalBoolean()
  thyroidTrouble?: boolean | null;

  @IsOptionalBoolean()
  heatOrColdIntolerance?: boolean | null;

  @IsOptionalBoolean()
  excessiveSweating?: boolean | null;

  @IsOptionalBoolean()
  excessiveThirstOrHunger?: boolean | null;

  @IsOptionalBoolean()
  polyuriaEndocrine?: boolean | null;

  @IsOptionalBoolean()
  changeInGloveOrShoeSize?: boolean | null;
}
