// src/appointment/dtos/create-prescriptionForm.dto.ts
import { IsOptional, IsString, IsUUID, IsNotEmpty } from 'class-validator';

export class CreatePrescriptionFormDto {
  @IsUUID()
  @IsNotEmpty()
  appointmentId: string;

  @IsString()
  @IsNotEmpty()
  drugName: string;

  @IsString()
  @IsNotEmpty()
  duration: string;

  @IsString()
  @IsNotEmpty()
  dosage: string;

  @IsString()
  @IsNotEmpty()
  frequency: string;

  @IsOptional()
  @IsString()
  instructions?: string;
}
