// src/appointment/dtos/create-preAppointmentForm.dto.ts
import {
  IsOptional,
  IsString,
  IsArray,
  IsEnum,
  IsUUID,
  IsNotEmpty,
  IsBoolean,
} from 'class-validator';
import { IsOptionalBoolean } from 'src/common/decorators/isOptionalBoolean.decorator';
import {
  ConstitutionalSymptoms,
  EyeProblems,
  EnmtProblems,
  CardiovascularProblems,
  RespiratoryProblems,
  GastrointestinalProblems,
  GenitourinaryProblems,
  SkinProblems,
  SleepProblems,
  NeurologicalProblems,
  MusculoskeletalProblems,
  PsychiatricProblems,
  EndocrineProblems,
  HematologicProblems,
  AllergicProblems,
} from '../entities/preAppointmentForm.entity';

export class CreatePreAppointmentFormDto {
  @IsUUID()
  @IsNotEmpty()
  appointmentId: string;

  @IsOptional()
  @IsBoolean()
  isMedCertificateRequested?: boolean;

  @IsOptional()
  @IsString()
  agendaForAppointment?: string;

  @IsOptional()
  @IsString()
  goalForHealth?: string;

  // DIABETES
  @IsOptionalBoolean()
  diabetes_isProblemWithMedication?: boolean | null;

  @IsOptional()
  @IsString()
  diabetes_homeGlucoseReadings?: string;

  // HIGH BP
  @IsOptionalBoolean()
  highBloodPressure_isProblemWithMedication?: boolean | null;

  @IsOptional()
  @IsString()
  highBloodPressure_homeBpReadings?: string;

  // DEPRESSION
  @IsOptionalBoolean()
  depression_isProblemWithMedication?: boolean | null;

  @IsOptionalBoolean()
  depression_isSuicidalThoughts?: boolean | null;

  // HIGH CHOLESTEROL
  @IsOptionalBoolean()
  highCholesterol_isProblemWithMedication?: boolean | null;

  // OTHER VISITS
  @IsOptionalBoolean()
  otherMedVisit?: boolean | null;

  @IsOptional()
  @IsString()
  otherMedVisit_details?: string;

  // EXERCISE
  @IsOptional()
  @IsString()
  exercice?: string;

  @IsOptional()
  @IsString()
  exercice_howLong?: string;

  @IsOptional()
  @IsString()
  exercice_howOften?: string;

  // SHORTNESS OF BREATH
  @IsOptionalBoolean()
  isShortnessOfBreath?: boolean | null;

  // SMOKING
  @IsOptionalBoolean()
  smoking_isInterestedInQuitting?: boolean | null;

  @IsOptional()
  @IsString()
  smoking_howMuch?: string;

  // FALLS
  @IsOptionalBoolean()
  fallenInPastyear?: boolean;

  @IsOptionalBoolean()
  problemWithWalkingBalance?: string;

  // ALCOHOL
  @IsOptional()
  @IsString()
  alcohol_numberOfDrinks_week?: string;

  @IsOptional()
  @IsString()
  alcohol_numberOfDrinks_day?: string;

  @IsOptionalBoolean()
  alcohol_moreThanFourDrinks?: boolean | null;

  @IsOptionalBoolean()
  alcohol_othersConcerned?: boolean | null;

  // CAFFEINE
  @IsOptional()
  @IsString()
  caffeinePerDay?: string;

  // BIRTH CONTROL
  @IsOptional()
  @IsString()
  birthControlMethod?: string;

  // SAFETY
  @IsOptionalBoolean()
  isUnsafeRelationship?: boolean | null;

  @IsOptionalBoolean()
  isWearSeatbelt?: boolean | null;

  // HIV
  @IsOptionalBoolean()
  isHivTestRequested?: boolean | null;

  // SLEEP
  @IsOptionalBoolean()
  isSleepApnea?: boolean | null;

  // DEPRESSION SCREEN
  @IsOptionalBoolean()
  isFeelingDown?: boolean | null;

  // MEDICATION
  @IsOptionalBoolean()
  isProblemWithMedication?: boolean | null;

  @IsOptional()
  @IsString()
  problemWithMedicationDetails?: string;

  // BLADDER CONTROL
  @IsOptionalBoolean()
  isProblemBladderControl?: boolean | null;

  // END OF LIFE CARE
  @IsOptionalBoolean()
  discussEndOfLifeCare?: boolean | null;

  // UPDATE
  @IsOptional()
  @IsString()
  newIllnessInFamily?: string;

  @IsOptional()
  @IsString()
  newDrugAllergies?: string;

  // SYMPTOMS ARRAYS
  @IsOptional()
  @IsArray()
  @IsEnum(ConstitutionalSymptoms, { each: true })
  constitutionalSymptoms?: ConstitutionalSymptoms[];

  @IsOptional()
  @IsArray()
  @IsEnum(EyeProblems, { each: true })
  eyeProblems?: EyeProblems[];

  @IsOptional()
  @IsArray()
  @IsEnum(EnmtProblems, { each: true })
  enmtProblems?: EnmtProblems[];

  @IsOptional()
  @IsArray()
  @IsEnum(CardiovascularProblems, { each: true })
  cardiovascularProblems?: CardiovascularProblems[];

  @IsOptional()
  @IsArray()
  @IsEnum(RespiratoryProblems, { each: true })
  respiratoryProblems?: RespiratoryProblems[];

  @IsOptional()
  @IsArray()
  @IsEnum(GastrointestinalProblems, { each: true })
  gastrointestinalProblems?: GastrointestinalProblems[];

  @IsOptional()
  @IsArray()
  @IsEnum(GenitourinaryProblems, { each: true })
  genitourinaryProblems?: GenitourinaryProblems[];

  @IsOptional()
  @IsArray()
  @IsEnum(SkinProblems, { each: true })
  skinProblems?: SkinProblems[];

  @IsOptional()
  @IsArray()
  @IsEnum(SleepProblems, { each: true })
  sleepProblems?: SleepProblems[];

  @IsOptional()
  @IsArray()
  @IsEnum(NeurologicalProblems, { each: true })
  neurologicalProblems?: NeurologicalProblems[];

  @IsOptional()
  @IsArray()
  @IsEnum(MusculoskeletalProblems, { each: true })
  musculoskeletalProblems?: MusculoskeletalProblems[];

  @IsOptional()
  @IsArray()
  @IsEnum(PsychiatricProblems, { each: true })
  psychiatricProblems?: PsychiatricProblems[];

  @IsOptional()
  @IsArray()
  @IsEnum(EndocrineProblems, { each: true })
  endocrineProblems?: EndocrineProblems[];

  @IsOptional()
  @IsArray()
  @IsEnum(HematologicProblems, { each: true })
  hematologicProblems?: HematologicProblems[];

  @IsOptional()
  @IsArray()
  @IsEnum(AllergicProblems, { each: true })
  allergicProblems?: AllergicProblems[];
}
