// src/appointment/dtos/create-soapForm.dto.ts
import {
  IsOptional,
  IsString,
  IsUUID,
  IsNotEmpty,
} from 'class-validator';

export class CreateSoapFormDto {
  @IsUUID()
  @IsNotEmpty()
  appointmentId: string;

  @IsString()
  @IsNotEmpty()
  cc: string;

  @IsString()
  @IsNotEmpty()
  historyOfPatientIllness: string;

  @IsString()
  @IsNotEmpty()
  ros: string;

  @IsString()
  @IsNotEmpty()
  pmh: string;

  @IsString()
  @IsNotEmpty()
  psh: string;

  @IsString()
  @IsNotEmpty()
  meds: string;

  @IsString()
  @IsNotEmpty()
  allergies: string;

  @IsString()
  @IsNotEmpty()
  fh: string;

  @IsString()
  @IsNotEmpty()
  sh: string;

  @IsString()
  @IsNotEmpty()
  vitalSigns: string;

  @IsString()
  @IsNotEmpty()
  gen: string;

  @IsString()
  @IsNotEmpty()
  heart: string;

  @IsString()
  @IsNotEmpty()
  lungs: string;

  @IsString()
  @IsNotEmpty()
  abdomen: string;

  @IsString()
  @IsNotEmpty()
  otherSystem: string;

  @IsString()
  @IsNotEmpty()
  ose: string;

  @IsString()
  @IsNotEmpty()
  patientFirstProblem: string;

  @IsString()
  @IsNotEmpty()
  patientSecondProblem: string;

  @IsString()
  @IsNotEmpty()
  patientThirdProblem: string;

  @IsString()
  @IsNotEmpty()
  somaticDysfunction: string;

  @IsString()
  @IsNotEmpty()
  other: string;

  @IsString()
  @IsNotEmpty()
  sympotomatic: string;

  @IsString()
  @IsNotEmpty()
  diagnosticTests: string;

  @IsString()
  @IsNotEmpty()
  testsNextSteps: string;

  @IsString()
  @IsNotEmpty()
  prescribeMeds: string;

  @IsString()
  @IsNotEmpty()
  omtPerformed: string;

  @IsString()
  @IsNotEmpty()
  education: string;
}
