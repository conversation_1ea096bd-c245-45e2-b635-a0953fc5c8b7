// src/appointment/dtos/fetch-doctor-appointments.dto.ts
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsDateString,
  ValidateIf,
} from 'class-validator';
import { AppointmentStatus } from '../entities/appointment.entity';

export class FetchDoctorAppointmentsDto {
  @IsOptional()
  @IsEnum(AppointmentStatus)
  appointmentStatus?: AppointmentStatus;

  @IsOptional()
  @IsString()
  searchText?: string;

  @IsOptional()
  @IsDateString()
  @ValidateIf((o) => o.endDate !== undefined)
  startDate?: string;

  @IsOptional()
  @IsDateString()
  @ValidateIf((o) => o.startDate !== undefined)
  endDate?: string;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  offset?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number;
}
