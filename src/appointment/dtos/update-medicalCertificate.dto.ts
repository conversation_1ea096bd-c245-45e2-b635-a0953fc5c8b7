// src/appointment/dtos/update-medicalCertificate.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { CreateMedicalCertificateDto } from './create-medicalCertificate.dto';
import { IsOptional, IsUUID } from 'class-validator';

export class UpdateMedicalCertificateDto extends PartialType(CreateMedicalCertificateDto) {
  @IsOptional()
  @IsUUID()
  id?: string;

  @IsOptional()
  @IsUUID()
  appointmentId?: string;
}
