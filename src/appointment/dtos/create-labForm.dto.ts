// src/appointment/dtos/create-labForm.dto.ts
import {
  IsOptional,
  IsString,
  IsUUID,
  IsNotEmpty,
  IsEnum,
  IsArray,
  IsDateString,
} from 'class-validator';
import { IsOptionalBoolean } from 'src/common/decorators/isOptionalBoolean.decorator';
import {
  PaymentResponsibility,
  Chemistry1Tests,
  HematologyTests,
  CoagulationTests,
  ImmunologyTests,
  CardiacFunctionAndLipidTests,
  ToleranceTests,
  NutritionalStatusTests,
  EndocrineAndTumorMarkers,
  SerumToxicologyTests,
  BloodGasesTests,
} from '../entities/labForm.entity';

export class CreateLabFormDto {
  @IsUUID()
  @IsNotEmpty()
  appointmentId: string;

  @IsOptional()
  @IsEnum(PaymentResponsibility)
  paymentResponsibility?: PaymentResponsibility;

  @IsOptionalBoolean()
  fastingRequired?: boolean | null;

  @IsOptional()
  @IsArray()
  @IsEnum(Chemistry1Tests, { each: true })
  chemistry1Tests?: Chemistry1Tests[];

  @IsOptional()
  @IsArray()
  @IsEnum(HematologyTests, { each: true })
  hematologyTests?: HematologyTests[];

  @IsOptional()
  @IsArray()
  @IsEnum(CoagulationTests, { each: true })
  coagulationTests?: CoagulationTests[];

  @IsOptional()
  @IsArray()
  @IsEnum(ImmunologyTests, { each: true })
  immunologyTests?: ImmunologyTests[];

  @IsOptional()
  @IsArray()
  @IsEnum(CardiacFunctionAndLipidTests, { each: true })
  cardiacFunctionAndLipidTests?: CardiacFunctionAndLipidTests[];

  @IsOptional()
  @IsArray()
  @IsEnum(ToleranceTests, { each: true })
  toleranceTests?: ToleranceTests[];

  @IsOptional()
  @IsArray()
  @IsEnum(NutritionalStatusTests, { each: true })
  nutritionalStatusTests?: NutritionalStatusTests[];

  @IsOptional()
  @IsArray()
  @IsEnum(EndocrineAndTumorMarkers, { each: true })
  endocrineAndTumorMarkers?: EndocrineAndTumorMarkers[];

  @IsOptional()
  @IsArray()
  @IsEnum(SerumToxicologyTests, { each: true })
  serumToxicologyTests?: SerumToxicologyTests[];

  @IsOptional()
  @IsArray()
  @IsEnum(BloodGasesTests, { each: true })
  bloodGases?: BloodGasesTests[];

  @IsOptional()
  @IsString()
  o2Device?: string;

  @IsOptional()
  @IsString()
  o2Therapy?: string;

  @IsOptional()
  @IsString()
  temprature?: string;

  @IsOptional()
  @IsDateString()
  digoxinLastDose?: Date;

  @IsOptional()
  @IsDateString()
  lithiumLastDose?: Date;

  @IsOptional()
  @IsDateString()
  phenobarbitalLastDose?: Date;

  @IsOptional()
  @IsDateString()
  phenytoinLastDose?: Date;

  @IsOptional()
  @IsDateString()
  primidoneLastDose?: Date;

  @IsOptional()
  @IsDateString()
  valproicAcidLastDose?: Date;

  @IsOptional()
  @IsDateString()
  cyclosporineLastDose?: Date;

  @IsOptional()
  @IsDateString()
  vancomycinLastDose?: Date;

  @IsOptional()
  @IsDateString()
  preDoseLastDose?: Date;

  @IsOptional()
  @IsDateString()
  postDoseLastDose?: Date;

  @IsOptional()
  @IsDateString()
  gentamicinLastDose?: Date;

  @IsOptional()
  @IsDateString()
  tobramycinLastDose?: Date;

  @IsOptional()
  @IsDateString()
  preDoseLevelLastDose?: Date;

  @IsOptional()
  @IsDateString()
  extendedIntervalPediatricsLastDose?: Date;

  @IsOptional()
  @IsDateString()
  postDoseLevelLastDose?: Date;

  @IsOptional()
  @IsDateString()
  extendedIntervalLastDose?: Date;

  @IsOptional()
  @IsDateString()
  hr22PostLevelNeonatesLastDose?: Date;
}
