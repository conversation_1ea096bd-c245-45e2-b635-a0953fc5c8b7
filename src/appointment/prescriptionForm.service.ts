// src/appointment/prescriptionForm.service.ts
import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, ILike, Between, Not, IsNull } from 'typeorm';
import { PrescriptionForm } from './entities/prescriptionForm.entity';
import { Appointment } from './entities/appointment.entity';
import { CreatePrescriptionFormDto } from './dtos/create-prescriptionForm.dto';
import { UpdatePrescriptionFormDto } from './dtos/update-prescriptionForm.dto';
import { FetchPatientPrescriptionsDto } from './dtos/fetch-patient-prescriptions.dto';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';
import { UserType } from 'src/common/enums/userType.enum';
import { AppointmentStatus } from './entities/appointment.entity';
import { EmrService } from 'src/emr/emr.service';

@Injectable()
export class PrescriptionFormService {
  // CONSTRUCTOR
  constructor(
    @InjectRepository(PrescriptionForm)
    private readonly prescriptionFormRepository: Repository<PrescriptionForm>,
    @InjectRepository(Appointment)
    private readonly appointmentRepository: Repository<Appointment>,
    @Inject(forwardRef(() => EmrService))
    private readonly emrService: EmrService,
  ) {}

  // SERVICES
  async createPrescriptionForm(
    user: DoctorWithUserType,
    createPrescriptionFormDto: CreatePrescriptionFormDto,
  ): Promise<PrescriptionForm> {
    const { appointmentId, ...formData } = createPrescriptionFormDto;

    // FIND APPOINTMENT WITH RELATIONS
    const appointment = await this.appointmentRepository.findOne({
      where: { id: appointmentId },
      relations: [
        'patient',
        'patient.profilePicture',
        'doctor',
        'doctor.profilePicture',
        'doctor.signature',
      ],
    });

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    // CHECK IF USER IS THE BOOKED DOCTOR
    if (appointment.doctor.id !== user.id) {
      throw new ForbiddenException(
        'Only the booked doctor can create prescription forms',
      );
    }

    // CHECK IF APPOINTMENT STATUS IS UPCOMING OR COMPLETED
    if (
      appointment.appointmentStatus !== AppointmentStatus.UPCOMING &&
      appointment.appointmentStatus !== AppointmentStatus.COMPLETED
    ) {
      throw new BadRequestException(
        'Prescription forms can only be created for upcoming or completed appointments',
      );
    }

    // CREATE FORM
    const prescriptionForm = this.prescriptionFormRepository.create({
      ...formData,
      appointment,
    });

    return await this.prescriptionFormRepository.save(prescriptionForm);
  }
  async updatePrescriptionForm(
    user: DoctorWithUserType,
    updatePrescriptionFormDto: UpdatePrescriptionFormDto,
  ): Promise<PrescriptionForm> {
    const { id, ...updateData } = updatePrescriptionFormDto;

    // FIND PRESCRIPTION FORM BY ID
    const prescriptionForm = await this.prescriptionFormRepository.findOne({
      where: { id },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    if (!prescriptionForm) {
      throw new NotFoundException('Prescription form not found');
    }

    // CHECK IF USER IS THE BOOKED DOCTOR
    if (prescriptionForm.appointment.doctor.id !== user.id) {
      throw new ForbiddenException(
        'Only the booked doctor can edit prescription forms',
      );
    }

    // CHECK IF APPOINTMENT STATUS IS UPCOMING OR COMPLETED
    if (
      prescriptionForm.appointment.appointmentStatus !==
        AppointmentStatus.UPCOMING &&
      prescriptionForm.appointment.appointmentStatus !==
        AppointmentStatus.COMPLETED
    ) {
      throw new BadRequestException(
        'Prescription forms can only be updated for upcoming or completed appointments',
      );
    }

    // UPDATE FORM
    Object.assign(prescriptionForm, updateData);
    return await this.prescriptionFormRepository.save(prescriptionForm);
  }
  async deletePrescriptionForm(
    user: DoctorWithUserType,
    id: string,
  ): Promise<void> {
    // FIND FORM WITH RELATIONS
    const prescriptionForm = await this.prescriptionFormRepository.findOne({
      where: { id },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
      ],
    });

    if (!prescriptionForm) {
      throw new NotFoundException('Prescription form not found');
    }

    // CHECK IF USER IS THE BOOKED DOCTOR
    if (prescriptionForm.appointment.doctor.id !== user.id) {
      throw new ForbiddenException(
        'Only the booked doctor can delete prescription forms',
      );
    }

    // DELETE FORM
    await this.prescriptionFormRepository.remove(prescriptionForm);
  }
  async getPrescriptionFormById(
    user: DoctorWithUserType | PatientWithUserType,
    id: string,
  ): Promise<PrescriptionForm> {
    // FIND FORM WITH RELATIONS
    const prescriptionForm = await this.prescriptionFormRepository.findOne({
      where: { id },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    if (!prescriptionForm) {
      throw new NotFoundException('Prescription form not found');
    }

    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN FORMS
      if (prescriptionForm.appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'You can only view prescription forms for your own appointments',
        );
      }
    } else if (user.userType === UserType.DOCTOR) {
      // DOCTOR CAN VIEW IF THEY ARE THE BOOKED DOCTOR OR HAVE SHARED EMR ACCESS
      const isBookedDoctor = prescriptionForm.appointment.doctor.id === user.id;
      const hasSharedAccess = await this.emrService.hasDoctorSharedEmrAccess(
        user.id,
        prescriptionForm.appointment.patient.id,
      );

      if (!isBookedDoctor && !hasSharedAccess) {
        throw new ForbiddenException(
          'You can only view prescription forms for your appointments or patients with shared EMR access',
        );
      }
    }

    return prescriptionForm;
  }
  async getPrescriptionFormsByAppointmentId(
    user: DoctorWithUserType | PatientWithUserType,
    appointmentId: string,
  ): Promise<PrescriptionForm[]> {
    // FIND APPOINTMENT WITH RELATIONS
    const appointment = await this.appointmentRepository.findOne({
      where: { id: appointmentId },
      relations: [
        'patient',
        'patient.profilePicture',
        'doctor',
        'doctor.profilePicture',
        'doctor.signature',
      ],
    });

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN FORMS
      if (appointment.patient.id !== user.id) {
        throw new ForbiddenException(
          'You can only view prescription forms for your own appointments',
        );
      }
    } else if (user.userType === UserType.DOCTOR) {
      // DOCTOR CAN VIEW IF THEY ARE THE BOOKED DOCTOR OR HAVE SHARED EMR ACCESS
      const isBookedDoctor = appointment.doctor.id === user.id;
      const hasSharedAccess = await this.emrService.hasDoctorSharedEmrAccess(
        user.id,
        appointment.patient.id,
      );

      if (!isBookedDoctor && !hasSharedAccess) {
        throw new ForbiddenException(
          'You can only view prescription forms for your appointments or patients with shared EMR access',
        );
      }
    }

    // FETCH ALL PRESCRIPTION FORMS FOR THE APPOINTMENT
    const prescriptionForms = await this.prescriptionFormRepository.find({
      where: { appointment: { id: appointmentId } },
      relations: [
        'appointment',
        'appointment.patient',
        'appointment.patient.profilePicture',
        'appointment.doctor',
        'appointment.doctor.profilePicture',
        'appointment.doctor.signature',
      ],
    });

    return prescriptionForms;
  }
  async getPrescriptionFormsByPatient(
    user: DoctorWithUserType | PatientWithUserType,
    patientId: string,
    fetchPatientPrescriptionsDto?: FetchPatientPrescriptionsDto,
  ): Promise<{
    appointments: Appointment[];
    total: number;
    offset: number;
    limit: number;
  }> {
    // CHECK AUTHORIZATION FOR PATIENT
    if (user.userType === UserType.PATIENT && user.id !== patientId) {
      throw new ForbiddenException(
        'You can only view your own prescription forms',
      );
    }

    // DOCTORS CAN VIEW PATIENT FORMS IF THEY HAVE APPOINTMENTS WITH PATIENT OR SHARED EMR ACCESS
    if (user.userType === UserType.DOCTOR) {
      const appointment = await this.appointmentRepository.findOne({
        where: {
          doctor: { id: user.id },
          patient: { id: patientId },
        },
      });
      const hasAppointments = !!appointment;

      const hasSharedEmrAccess = await this.emrService.hasDoctorSharedEmrAccess(
        user.id,
        patientId,
      );

      if (!hasAppointments && !hasSharedEmrAccess) {
        throw new ForbiddenException(
          "Not authorized to view this patient's prescription forms",
        );
      }
    }

    const {
      searchText,
      startDate,
      endDate,
      offset = 0,
      limit = 20,
    } = fetchPatientPrescriptionsDto || {};

    // VALIDATE DATE RANGE
    if ((startDate && !endDate) || (!startDate && endDate)) {
      throw new BadRequestException(
        'Both startDate and endDate must be provided together',
      );
    }

    if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
      throw new BadRequestException(
        'startDate must be before or equal to endDate',
      );
    }

    // BUILD WHERE CONDITIONS FOR APPOINTMENTS WITH PRESCRIPTION FORMS
    const baseWhereConditions: any = {
      patient: { id: patientId },
      prescriptionForms: Not(IsNull()), // ENSURES APPOINTMENT HAS PRESCRIPTION FORMS
    };

    // ADD DATE RANGE FILTER
    if (startDate && endDate) {
      baseWhereConditions.startDateTime = Between(
        new Date(startDate),
        new Date(endDate),
      );
    }

    // ADD SEARCH CONDITIONS
    const searchConditions = [];
    if (searchText) {
      searchConditions.push(
        // SEARCH BY DOCTOR FIRST NAME
        {
          ...baseWhereConditions,
          doctor: {
            firstName: ILike(`%${searchText}%`),
          },
        },
        // SEARCH BY DOCTOR LAST NAME
        {
          ...baseWhereConditions,
          doctor: {
            lastName: ILike(`%${searchText}%`),
          },
        },
        // SEARCH BY DOCTOR EMAIL
        {
          ...baseWhereConditions,
          doctor: {
            email: ILike(`%${searchText}%`),
          },
        },
        // SEARCH BY DOCTOR CONTACT NUMBER
        {
          ...baseWhereConditions,
          doctor: {
            contactNumber: ILike(`%${searchText}%`),
          },
        },
        // SEARCH BY CONSULTATION SERVICE NAME
        {
          ...baseWhereConditions,
          consultation: {
            serviceName: ILike(`%${searchText}%`),
          },
        },
      );
    }

    const finalWhereConditions = searchText
      ? searchConditions
      : baseWhereConditions;

    // GET APPOINTMENTS WITH PRESCRIPTION FORMS (FILTERED AT DB LEVEL)
    const [appointments, total] = await this.appointmentRepository.findAndCount(
      {
        where: finalWhereConditions,
        relations: [
          'patient',
          'patient.profilePicture',
          'doctor',
          'doctor.profilePicture',
          'doctor.signature',
          'consultation',
          'prescriptionForms',
        ],
        order: { startDateTime: 'DESC' },
        skip: offset,
        take: limit,
      },
    );

    return {
      appointments,
      total,
      offset,
      limit,
    };
  }
}
