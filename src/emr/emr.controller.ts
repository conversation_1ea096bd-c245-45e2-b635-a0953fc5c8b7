// src/emr/emr.controller.ts
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { EmrService } from './emr.service';
import { ShareEmrDto } from './dtos/share-emr.dto';
import { FetchSharedEmrsDto } from './dtos/fetch-shared-emrs.dto';
import { AuthenticationGuard } from 'src/auth/guards/authentication.guard';
import { AuthorizationGuard } from 'src/auth/guards/authorization.guard';
import { UserTypes } from 'src/common/decorators/userType.decorator';
import { UserType } from 'src/common/enums/userType.enum';
import { RequestWithUser } from 'src/common/interfaces/requestWithUser.interface';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';

@Controller('api/emr')
export class EmrController {
  // CONSTRUCTOR
  constructor(private readonly emrService: EmrService) {}

  // CONTROLLERS
  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Post('share')
  async shareEmr(
    @Body() shareEmrDto: ShareEmrDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.emrService.shareEmr(user, shareEmrDto);
    return { message: 'EMR shared successfully', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Get('getSharedEmrs')
  async getSharedEmrs(
    @Query() fetchSharedEmrsDto: FetchSharedEmrsDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.emrService.getSharedEmrs(user, fetchSharedEmrsDto);
    return { message: 'Shared EMRs fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Get('getConcernedDoctors/:patientId')
  async getConcernedDoctors(@Param('patientId') patientId: string) {
    const data = await this.emrService.getConcernedDoctors(patientId);
    return { message: 'Concerned doctors fetched successfully', data };
  }
}
