// src/emr/emr.module.ts
import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Emr } from './entities/emr.entity';
import { EmrService } from './emr.service';
import { EmrController } from './emr.controller';
import { AuthModule } from 'src/auth/auth.module';
import { DoctorModule } from 'src/doctor/doctor.module';
import { PatientModule } from 'src/patient/patient.module';
import { AppointmentModule } from 'src/appointment/appointment.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Emr]),
    forwardRef(() => AuthModule),
    forwardRef(() => DoctorModule),
    forwardRef(() => PatientModule),
    forwardRef(() => AppointmentModule),
  ],
  controllers: [EmrController],
  providers: [EmrService],
  exports: [EmrService],
})
export class EmrModule {}
