// src/emr/emr.service.ts
import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, Like } from 'typeorm';
import { Emr } from './entities/emr.entity';
import { ShareEmrDto } from './dtos/share-emr.dto';
import { FetchSharedEmrsDto } from './dtos/fetch-shared-emrs.dto';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { DoctorService } from 'src/doctor/doctor.service';
import { PatientService } from 'src/patient/patient.service';
import { AppointmentService } from 'src/appointment/appointment.service';

@Injectable()
export class EmrService {
  // CONSTRUCTOR
  constructor(
    @InjectRepository(Emr)
    private readonly emrRepository: Repository<Emr>,
    @Inject(forwardRef(() => DoctorService))
    private readonly doctorService: DoctorService,
    @Inject(forwardRef(() => PatientService))
    private readonly patientService: PatientService,
    @Inject(forwardRef(() => AppointmentService))
    private readonly appointmentService: AppointmentService,
  ) {}

  // SERVICES
  async shareEmr(
    user: DoctorWithUserType,
    shareEmrDto: ShareEmrDto,
  ): Promise<Emr> {
    // VERIFY TARGET DOCTOR
    const targetDoctor = await this.doctorService.findById(
      shareEmrDto.doctorId,
    );
    if (!targetDoctor) {
      throw new BadRequestException('Target doctor not found');
    }

    // VERIFY PATIENT
    const patient = await this.patientService.findById(shareEmrDto.patientId);
    if (!patient) {
      throw new BadRequestException('Patient not found');
    }

    // CHECK IF TARGET DOCTOR IS ALREADY A CONCERNED DOCTOR
    const targetDoctorAppointments =
      await this.appointmentService.getDoctorsWithPatientAppointments(
        shareEmrDto.patientId,
      );
    const targetHasAppointments = targetDoctorAppointments.some(
      (doctor) => doctor.id === shareEmrDto.doctorId,
    );

    const targetHasSharedEmrAccess = await this.hasDoctorSharedEmrAccess(
      shareEmrDto.doctorId,
      shareEmrDto.patientId,
    );

    if (targetHasAppointments || targetHasSharedEmrAccess) {
      throw new BadRequestException(
        'Doctor already has access to this patient',
      );
    }

    // CHECK IF SHARING DOCTOR IS CONCERNED WITH PATIENT (HAS APPOINTMENTS OR SHARED EMR ACCESS)
    // CHECK APPOINTMENTS USING APPOINTMENT SERVICE HELPER FUNCTION
    const appointmentDoctors =
      await this.appointmentService.getDoctorsWithPatientAppointments(
        shareEmrDto.patientId,
      );
    const hasAppointments = appointmentDoctors.some(
      (doctor) => doctor.id === user.id,
    );

    // CHECK SHARED EMR ACCESS USING EMR SERVICE HELPER FUNCTION
    const hasSharedEmrAccess = await this.hasDoctorSharedEmrAccess(
      user.id,
      shareEmrDto.patientId,
    );

    if (!hasAppointments && !hasSharedEmrAccess) {
      throw new ForbiddenException(
        'You can only share EMR for patients you have appointments with or whose EMR has been shared with you',
      );
    }

    // CHECK IF EMR IS ALREADY SHARED
    const existingShare = await this.emrRepository.findOne({
      where: {
        patient: { id: shareEmrDto.patientId },
        sharedBy: { id: user.id },
        sharedTo: { id: shareEmrDto.doctorId },
      },
    });

    if (existingShare) {
      throw new BadRequestException('EMR is already shared with this doctor');
    }

    // CREATE SHARED EMR
    const sharedEmr = this.emrRepository.create({
      patient,
      sharedBy: user,
      sharedTo: targetDoctor,
    });

    return this.emrRepository.save(sharedEmr);
  }
  async getSharedEmrs(
    user: DoctorWithUserType,
    fetchSharedEmrsDto?: FetchSharedEmrsDto,
  ): Promise<{
    sharedEmrs: Emr[];
    total: number;
    offset: number;
    limit: number;
  }> {
    const {
      searchText,
      startDate,
      endDate,
      offset = 0,
      limit = 20,
    } = fetchSharedEmrsDto || {};

    // VALIDATE DATE RANGE
    if ((startDate && !endDate) || (!startDate && endDate)) {
      throw new BadRequestException(
        'Both startDate and endDate must be provided together',
      );
    }

    if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
      throw new BadRequestException(
        'startDate must be before or equal to endDate',
      );
    }

    // BASE WHERE CONDITIONS - EMRS SHARED BY OR TO THIS DOCTOR
    const baseWhereConditions = [
      { sharedBy: { id: user.id } },
      { sharedTo: { id: user.id } },
    ];

    // APPLY DATE FILTER
    let whereConditions = baseWhereConditions;
    if (startDate && endDate) {
      whereConditions = baseWhereConditions.map((condition) => ({
        ...condition,
        createdAt: Between(new Date(startDate), new Date(endDate)),
      }));
    }

    // APPLY SEARCH TEXT FILTER
    let finalWhereConditions: any = whereConditions;
    if (searchText) {
      const searchPattern = `%${searchText}%`;
      finalWhereConditions = [];

      whereConditions.forEach((condition) => {
        finalWhereConditions.push(
          // SEARCH BY PATIENT NAME
          {
            ...condition,
            patient: { firstName: Like(searchPattern) },
          },
          {
            ...condition,
            patient: { lastName: Like(searchPattern) },
          },
          // SEARCH BY PATIENT ID
          // {
          //   ...condition,
          //   patient: { id: Like(searchPattern) },
          // },
          // SEARCH BY SHARED BY DOCTOR NAME
          {
            ...condition,
            sharedBy: { firstName: Like(searchPattern) },
          },
          {
            ...condition,
            sharedBy: { lastName: Like(searchPattern) },
          },
          // SEARCH BY SHARED TO DOCTOR NAME
          {
            ...condition,
            sharedTo: { firstName: Like(searchPattern) },
          },
          {
            ...condition,
            sharedTo: { lastName: Like(searchPattern) },
          },
        );
      });
    }

    // GET SHARED EMRS WITH PAGINATION
    const [sharedEmrs, total] = await this.emrRepository.findAndCount({
      where: finalWhereConditions,
      relations: [
        'patient',
        'patient.profilePicture',
        'sharedBy',
        'sharedBy.profilePicture',
        'sharedTo',
        'sharedTo.profilePicture',
      ],
      order: { createdAt: 'DESC' },
      skip: offset,
      take: limit,
    });

    return {
      sharedEmrs,
      total,
      offset,
      limit,
    };
  }
  async getConcernedDoctors(patientId: string): Promise<{ doctors: any[] }> {
    // VERIFY PATIENT
    const patient = await this.patientService.findById(patientId);
    if (!patient) {
      throw new BadRequestException('Patient not found');
    }

    // GET DOCTORS WHO HAVE APPOINTMENTS WITH PATIENT
    const appointmentDoctors =
      await this.appointmentService.getDoctorsWithPatientAppointments(
        patientId,
      );

    // GET DOCTORS WHO HAVE SHARED EMR ACCESS
    const sharedEmrDoctors = await this.emrRepository.find({
      where: [{ patient: { id: patientId } }],
      relations: [
        'sharedBy',
        'sharedBy.profilePicture',
        'sharedBy.qualifications',
        'sharedTo',
        'sharedTo.profilePicture',
        'sharedTo.qualifications',
      ],
    });

    // COMBINE AND DEDUPLICATE DOCTORS
    const doctorMap = new Map();

    // ADD APPOINTMENT DOCTORS
    appointmentDoctors.forEach((doctor) => {
      doctorMap.set(doctor.id, {
        ...doctor,
        accessType: 'appointment',
      });
    });

    // ADD SHARED EMR DOCTORS
    sharedEmrDoctors.forEach((sharedEmr) => {
      // ADD SHARED BY DOCTOR
      if (!doctorMap.has(sharedEmr.sharedBy.id)) {
        doctorMap.set(sharedEmr.sharedBy.id, {
          ...sharedEmr.sharedBy,
          accessType: 'shared',
        });
      }

      // ADD SHARED TO DOCTOR
      if (!doctorMap.has(sharedEmr.sharedTo.id)) {
        doctorMap.set(sharedEmr.sharedTo.id, {
          ...sharedEmr.sharedTo,
          accessType: 'shared',
        });
      }
    });

    return {
      doctors: Array.from(doctorMap.values()),
    };
  }

  // HELPER FUNCTIONS
  // CHECKS IF DOCTOR HAS SHARED EMR ACCESS TO PATIENT
  async hasDoctorSharedEmrAccess(
    doctorId: string,
    patientId: string,
  ): Promise<boolean> {
    const hasSharedAccess = await this.emrRepository.findOne({
      where: [
        { patient: { id: patientId }, sharedBy: { id: doctorId } },
        { patient: { id: patientId }, sharedTo: { id: doctorId } },
      ],
    });

    return !!hasSharedAccess;
  }
}
