import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Response, Request } from 'express';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  catch(exception: any, host: ArgumentsHost) {
    console.log('EXCEPTION => ', exception);

    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let errorDetail = 'A detailed error message should be here';

    if (
      exception instanceof HttpException &&
      typeof exception.getResponse === 'function'
    ) {
      status = exception.getStatus();
      const res = exception.getResponse() as any; // Cast to any to allow property access.
      message = typeof res === 'string' ? res : res.message || message;
      errorDetail =
        typeof res === 'object' && res.error ? res.error : errorDetail;
    } else {
      message = exception.message || message;
      errorDetail = exception.error || errorDetail;
    }

    response.status(status).json({
      statusCode: status,
      message,
      error: errorDetail,
    });
  }
}
