// src/filters/response.interceptor.ts
import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { instanceToPlain } from 'class-transformer';

export interface ResponseEnvelope<T> {
  statusCode: number;
  message: string;
  data: T;
}

@Injectable()
export class ResponseInterceptor<T>
  implements NestInterceptor<T, ResponseEnvelope<T>>
{
  intercept(
    context: ExecutionContext,
    next: CallHandler<T>,
  ): Observable<ResponseEnvelope<T>> {
    const res = context.switchToHttp().getResponse();
    const statusCode = res.statusCode;

    return next.handle().pipe(
      map((result: any) => {
        let message = 'success';
        let finalData: any = null;

        // CHECK IF RESULT IS OBJECT AND NOT ARRAY
        if (result && typeof result === 'object' && !Array.isArray(result)) {
          // IF RESULT HAS BOTH MESSAGE AND DATA PROPERTIES
          if ('message' in result && 'data' in result) {
            message = result.message || message;
            const plainData =
              result.data !== undefined ? instanceToPlain(result.data) : null;
            finalData = preserveTimestamps(plainData);
          } else if ('message' in result) {
            // IF ONLY HAS MESSAGE, TREAT REST AS DATA
            message = result.message || message;
            const { message: _, ...rest } = result;
            const plainData =
              Object.keys(rest).length > 0 ? instanceToPlain(rest) : null;
            finalData = preserveTimestamps(plainData);
          } else {
            // TREAT ENTIRE OBJECT AS DATA
            const plainData =
              result !== undefined ? instanceToPlain(result) : null;
            finalData = preserveTimestamps(plainData);
          }
        } else {
          // FOR ARRAYS OR PRIMITIVES
          const plainData =
            result !== undefined ? instanceToPlain(result) : null;
          finalData = preserveTimestamps(plainData);
        }

        return {
          statusCode,
          message,
          data: finalData,
        };
      }),
    );
  }
}

// HELPER FUNCTIONS
// PRESERVES TIMESTAMPS WITHOUT UTC CONVERSION
function preserveTimestamps(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (obj instanceof Date) {
    // FORMAT DATE AS ISO STRING WITHOUT TIMEZONE CONVERSION
    const year = obj.getFullYear();
    const month = String(obj.getMonth() + 1).padStart(2, '0');
    const day = String(obj.getDate()).padStart(2, '0');
    const hours = String(obj.getHours()).padStart(2, '0');
    const minutes = String(obj.getMinutes()).padStart(2, '0');
    const seconds = String(obj.getSeconds()).padStart(2, '0');
    const milliseconds = String(obj.getMilliseconds()).padStart(3, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}Z`;
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => preserveTimestamps(item));
  }

  if (typeof obj === 'object') {
    const result: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        result[key] = preserveTimestamps(obj[key]);
      }
    }
    return result;
  }

  return obj;
}
