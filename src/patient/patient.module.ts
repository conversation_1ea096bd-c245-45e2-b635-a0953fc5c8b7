// src/patient/patient.module.ts
import { Module, forwardRef } from '@nestjs/common';
import { PatientController } from './patient.controller';
import { PatientService } from './patient.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Patient } from './entities/patient.entity';
import { AuthModule } from 'src/auth/auth.module';
import { FileModule } from 'src/file/file.module';
import { InsuranceDetails } from './entities/insuranceDetails.entity';
import { EmrModule } from 'src/emr/emr.module';
import { AppointmentModule } from 'src/appointment/appointment.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Patient, InsuranceDetails]),
    forwardRef(() => AuthModule),
    forwardRef(() => FileModule),
    forwardRef(() => EmrModule),
    forwardRef(() => AppointmentModule),
  ],
  controllers: [PatientController],
  providers: [PatientService],
  exports: [PatientService],
})
export class PatientModule {}
