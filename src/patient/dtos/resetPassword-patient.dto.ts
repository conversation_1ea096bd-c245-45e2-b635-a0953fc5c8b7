// src/patient/dto/resetPassword-patient.dto.ts

import { Transform } from 'class-transformer';
import { IsEmail, IsNotEmpty, IsString, MinLength } from 'class-validator';

export class ResetPasswordPatientDto {
  @IsEmail()
  @Transform(({ value }) => value.toLowerCase())
  email: string;

  @IsString()
  @IsNotEmpty()
  resetPasswordToken: string;

  @IsString()
  @MinLength(6)
  newPassword: string;
}
