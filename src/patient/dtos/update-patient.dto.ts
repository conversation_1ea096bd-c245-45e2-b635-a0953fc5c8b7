// src/patient/dto/update-patient.dto.ts

import {
  IsEmail,
  IsOptional,
  IsString,
  IsEnum,
  IsDate,
  IsArray,
  IsNumber,
  IsBoolean,
} from 'class-validator';
import { Gender } from '../entities/patient.entity';
import { Transform, Type } from 'class-transformer';

export class UpdateInsuranceDetailsDto {
  @IsOptional()
  @IsString()
  id?: string;

  @IsOptional()
  @IsString()
  patientRegisteredName?: string;

  @IsOptional()
  @IsString()
  companyLocation?: string;

  @IsOptional()
  @IsString()
  companyName?: string;

  @IsOptional()
  @IsString()
  planType?: string;

  @IsOptional()
  @IsString()
  policyOwner?: string;

  @IsOptional()
  @IsString()
  policyNumber?: string;

  @IsOptional()
  @IsString()
  groupNumber?: string;

  @IsOptional()
  @IsNumber()
  coPay?: number;

  @IsOptional()
  @IsString()
  planDetails?: string;

  @IsOptional()
  @IsBoolean()
  consentForMedInfoRelease?: boolean;

  @IsOptional()
  @IsBoolean()
  consentForFinances?: boolean;
}

export class UpdatePatientDto {
  @IsOptional()
  @IsString()
  patientId?: string;

  @IsOptional()
  @IsEmail()
  @Transform(({ value }) => value.toLowerCase())
  email?: string;

  @IsOptional()
  @IsString()
  firstName?: string;

  @IsOptional()
  @IsString()
  lastName?: string;

  @IsOptional()
  @IsString()
  contactNumber?: string;

  @IsOptional()
  @IsEnum(Gender)
  gender?: Gender;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  dob?: Date;

  @IsOptional()
  @IsString()
  address?: string;

  @IsOptional()
  @IsString()
  country?: string;

  @IsOptional()
  @IsString()
  medicalConcerns?: string;

  @IsOptional()
  insuranceDetails?: UpdateInsuranceDetailsDto;
}
