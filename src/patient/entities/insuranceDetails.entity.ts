// src/patient/entities/insuranceDetails.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToOne,
  JoinColumn,
  BeforeInsert,
  BeforeUpdate,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { Patient } from './patient.entity';

export enum InsuranceStatus {
  APPROVED = 'approved',
  PENDING = 'pending',
  REJECTED = 'rejected',
}

@Entity()
export class InsuranceDetails {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  patientRegisteredName: string;

  @Column({ nullable: true })
  companyLocation: string;

  @Column({ nullable: true })
  companyName: string;

  @Column({ nullable: true })
  planType: string;

  @Column({ nullable: true })
  policyOwner: string;

  @Column({ nullable: true })
  policyNumber: string;

  @Column({ nullable: true })
  groupNumber: string;

  @Column({ nullable: true })
  coPay: number;

  @Column({
    type: 'enum',
    enum: InsuranceStatus,
    default: InsuranceStatus.PENDING,
  })
  insuranceStatus: InsuranceStatus;

  @Column({ nullable: true })
  planDetails: string;

  @Column({ default: false })
  isInsuranceDetailsComplete: boolean;

  @Column({ default: false })
  consentForMedInfoRelease: boolean;

  @Column({ default: false })
  consentForFinances: boolean;

  @Exclude()
  @OneToOne(() => Patient, (patient) => patient.insuranceDetails, {
    onDelete: 'CASCADE',
  })
  patient: Patient;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @BeforeInsert()
  @BeforeUpdate()
  computeCompleteness() {
    this.isInsuranceDetailsComplete = !!(
      this.patientRegisteredName &&
      this.companyLocation &&
      this.companyName &&
      this.planType &&
      this.policyOwner &&
      this.policyNumber &&
      this.groupNumber &&
      this.coPay != null &&
      this.planDetails
    );
  }
}
