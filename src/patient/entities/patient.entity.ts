// src/patient/entities/patient.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  OneToOne,
  JoinColumn,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';

import { File } from 'src/file/entities/file.entity';
import { InsuranceDetails } from './insuranceDetails.entity';
import { Appointment } from 'src/appointment/entities/appointment.entity';
import { Emr } from 'src/emr/entities/emr.entity';
import { Exclude } from 'class-transformer';

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
}

@Entity()
export class Patient {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column()
  firstName: string;

  @Column()
  lastName: string;

  @Exclude()
  @Column()
  password: string;

  @Column({ nullable: true })
  contactNumber: string;

  @Column({ type: 'enum', enum: Gender, nullable: true })
  gender: Gender;

  @Column({ nullable: true, type: 'date' })
  dob: Date;

  @Column({ nullable: true })
  address: string;

  @Column({ nullable: true })
  country: string;

  @Column({ nullable: true })
  medicalConcerns: string;

  @Exclude()
  @Column({ nullable: true })
  otp: string;

  @Exclude()
  @Column({ nullable: true })
  otpExpires: Date;

  @Exclude()
  @Column({ nullable: true })
  resetPasswordToken: string;

  @Column({ default: false })
  isProfileComplete: boolean;

  // Profile Picture
  @OneToOne(() => File, { nullable: true })
  @JoinColumn()
  profilePicture: File;

  // Insurance Details
  @OneToOne(
    () => InsuranceDetails,
    (insuranceDetails) => insuranceDetails.patient,
    {
      cascade: true,
      nullable: true,
    },
  )
  @JoinColumn()
  insuranceDetails: InsuranceDetails;

  @OneToMany(() => Appointment, (appointment) => appointment.patient)
  appointments: Appointment[];

  @OneToMany(() => Emr, (emr) => emr.patient)
  sharedEmrs: Emr[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @BeforeInsert()
  @BeforeUpdate()
  computeCompleteness() {
    this.isProfileComplete = !!(
      this.email &&
      this.firstName &&
      this.lastName &&
      this.contactNumber &&
      this.gender &&
      this.dob &&
      this.address &&
      this.country &&
      this.profilePicture?.url &&
      this.medicalConcerns
    );
  }
}
