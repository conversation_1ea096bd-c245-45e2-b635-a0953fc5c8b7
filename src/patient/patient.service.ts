// src/patient/patient.service.ts
import {
  BadRequestException,
  ForbiddenException,
  forwardRef,
  Inject,
  Injectable,
} from '@nestjs/common';

import { InjectRepository } from '@nestjs/typeorm';
import { Repository, ILike } from 'typeorm';
import { Patient } from './entities/patient.entity';
import { CreatePatientDto } from './dtos/create-patient.dto';
import * as bcrypt from 'bcrypt';
import { UpdatePatientDto } from './dtos/update-patient.dto';
import { ForgotPasswordPatientDto } from './dtos/forgotPassword-patient.dto';
import { sendEmail, EmailOptions } from 'src/helpers/sendGrid.helper';
import { ResetPasswordPatientDto } from './dtos/resetPassword-patient.dto';
import { VerifyForgotPasswordOtpPatientDto } from './dtos/verifyForgotPasswordOtp-patient.dto';
import { randomBytes } from 'crypto';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';
import { File as MulterFile } from 'multer';
import { FileService } from 'src/file/file.service';
import { ChangePasswordPatientDto } from './dtos/changePassword-patient.dto';
import { InsuranceDetails } from './entities/insuranceDetails.entity';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { UserType } from 'src/common/enums/userType.enum';
import { EmrService } from 'src/emr/emr.service';
import { AppointmentService } from 'src/appointment/appointment.service';
import { FetchConcernedPatientsDto } from './dtos/fetch-concerned-patients.dto';

@Injectable()
export class PatientService {
  // CONSTRUCTOR
  constructor(
    @InjectRepository(Patient)
    private readonly patientRepository: Repository<Patient>,

    @Inject(forwardRef(() => FileService))
    private readonly fileService: FileService,

    @Inject(forwardRef(() => EmrService))
    private readonly emrService: EmrService,

    @Inject(forwardRef(() => AppointmentService))
    private readonly appointmentService: AppointmentService,
  ) {}

  // SERVICES
  async createPatient(createPatientDto: CreatePatientDto): Promise<Patient> {
    const { email, password, firstName, lastName, contactNumber } =
      createPatientDto;

    // CHECK EXSISTING PATIENT
    const exisitngPatient = await this.patientRepository.findOne({
      where: { email },
    });

    if (exisitngPatient) {
      throw new BadRequestException('A patient with this email already exists');
    }

    // PASSWORD HASH
    const hashedPassword = await bcrypt.hash(password, 10);

    // CREATE AND SAVE PATIENT
    const patient = this.patientRepository.create({
      email,
      password: hashedPassword,
      firstName,
      lastName,
      contactNumber,
    });

    return await this.patientRepository.save(patient);
  }
  // CAN BE CALLED BY PATIENT HIMSELF
  async updatePatient(
    updatePatientDto: UpdatePatientDto,
    files: MulterFile[],
    user: PatientWithUserType,
  ): Promise<Patient | undefined> {
    const { patientId } = updatePatientDto;
    let forSelf = false;
    let patientIdToUpdate = patientId;
    if (patientIdToUpdate) {
      if (patientIdToUpdate === user.id) {
        forSelf = true;
      } else {
        forSelf = false;
      }
    } else {
      forSelf = true;
      patientIdToUpdate = user.id;
    }

    if (!forSelf) {
      throw new BadRequestException("You cannot update other's data");
    }

    // CHECK TO MAINTAIN UNIQUE EMAILS
    if (updatePatientDto.email) {
      const existngPatient = await this.patientRepository.findOne({
        where: { email: updatePatientDto.email },
      });
      if (existngPatient && existngPatient.id !== patientIdToUpdate) {
        throw new BadRequestException('Email already in use');
      }
    }

    const patient = await this.patientRepository.findOne({
      where: { id: patientIdToUpdate },
      relations: ['profilePicture', 'insuranceDetails'],
    });
    if (!patient) {
      throw new BadRequestException('Patient not found');
    }

    const insDto = updatePatientDto.insuranceDetails;
    delete updatePatientDto.insuranceDetails;

    if (insDto) {
      if (patient.insuranceDetails) {
        Object.assign(patient.insuranceDetails, insDto);
      } else {
        const ins = new InsuranceDetails();
        Object.assign(ins, insDto);
        ins.patient = patient;
        patient.insuranceDetails = ins;
      }
    }

    Object.assign(patient, updatePatientDto);

    // PROFILE PICTURE
    const profilePictureFile = files.find(
      (file) => file.fieldname === 'profilePicture',
    );
    if (profilePictureFile) {
      const uploadedProfilePicture = await this.fileService.uploadFile(
        profilePictureFile,
        'Patient',
        patient.id,
      );

      patient.profilePicture = uploadedProfilePicture;
    }

    const updatedPatient = await this.patientRepository.save(patient);

    return updatedPatient;
  }
  async requestForgotPatientPassword(
    forgotPasswordPatientDto: ForgotPasswordPatientDto,
  ): Promise<void> {
    const patient = await this.patientRepository.findOne({
      where: { email: forgotPasswordPatientDto.email },
    });

    if (!patient) {
      throw new BadRequestException('No patient found against this email');
    }

    // GENERATE RESET OTP
    const randomNumber = Math.floor(Math.random() * 10000);
    const otp = randomNumber.toString().padStart(4, '0');

    patient.otp = otp;
    patient.otpExpires = new Date(Date.now() + 3600000); // 1 hour from now

    console.log('OTP => ', otp);

    const emailOptions: EmailOptions = {
      to: patient.email,
      subject: 'Reset Password',
      text: `Your OTP for resetting password is ${otp}`,
      html: `<strong>Your OTP for resetting password is ${otp}</strong>`,
    };

    await this.patientRepository.save(patient);
    return;
  }
  async verifyForgotPatientPasswordOtp(
    verifyForgotPasswordOtpPatientDto: VerifyForgotPasswordOtpPatientDto,
  ): Promise<string> {
    // CHECK PATIENT
    const patient = await this.patientRepository.findOne({
      where: { email: verifyForgotPasswordOtpPatientDto.email },
    });

    if (!patient) {
      throw new BadRequestException('No patient found against this email');
    }

    // CHECK OTP
    if (!patient.otp || patient.otp !== verifyForgotPasswordOtpPatientDto.otp) {
      throw new BadRequestException('Invalid OTP');
    }

    // CHECK EXPIRY
    const now = new Date();
    if (!patient.otpExpires || patient.otpExpires < now) {
      throw new BadRequestException('OTP has expired');
    }

    // CLEAR OTP
    patient.otp = null;
    patient.otpExpires = null;

    // GENREATE RESET PASSWORD TOKEN
    patient.resetPasswordToken = randomBytes(32).toString('hex');
    // SAVE PATIENT
    await this.patientRepository.save(patient);

    return patient.resetPasswordToken;
  }
  async resetPatientPassword(
    resetPasswordPatientDto: ResetPasswordPatientDto,
  ): Promise<void> {
    // CHECK PATIENT
    const patient = await this.patientRepository.findOne({
      where: { email: resetPasswordPatientDto.email },
    });

    if (!patient) {
      throw new BadRequestException('No patient found against this email');
    }

    // CHECK RESET PASSWORD TOKEN
    if (
      !patient.resetPasswordToken ||
      patient.resetPasswordToken !== resetPasswordPatientDto.resetPasswordToken
    ) {
      throw new BadRequestException('Invalid token');
    }

    // HASH PASSWORD
    const hashedPassword = await bcrypt.hash(
      resetPasswordPatientDto.newPassword,
      10,
    );
    patient.password = hashedPassword;

    // CLEAR RESET PASSWORD TOKEN
    patient.resetPasswordToken = null;

    // SAVE PATIENT
    await this.patientRepository.save(patient);
    return;
  }
  async changePatientPassword(
    changePasswordPatientDto: ChangePasswordPatientDto,
    user: PatientWithUserType,
  ): Promise<void> {
    // CHECK PATIENT
    const exisitngPatient = await this.patientRepository.findOne({
      where: { id: user.id },
    });

    if (!exisitngPatient) {
      throw new BadRequestException('No patient found against this id');
    }

    // VERIFY OLD PASSWORD
    const isPasswordCorrect = await bcrypt.compare(
      changePasswordPatientDto.oldPassword,
      exisitngPatient.password,
    );
    if (!isPasswordCorrect) {
      throw new BadRequestException('Wrong old password');
    }

    // CHECK FOR SAME PASSWORD
    const isPasswordSame = await bcrypt.compare(
      changePasswordPatientDto.newPassword,
      exisitngPatient.password,
    );
    if (isPasswordSame) {
      throw new BadRequestException(
        'New password shouldnt be same as old password',
      );
    }

    // HASH PASSWORD
    const hashedPassword = await bcrypt.hash(
      changePasswordPatientDto.newPassword,
      10,
    );
    exisitngPatient.password = hashedPassword;

    // SAVE PATIENT
    await this.patientRepository.save(exisitngPatient);
    return;
  }

  async fetchPatientById(
    user: DoctorWithUserType | PatientWithUserType,
    patientId: string,
  ): Promise<any> {
    // DETERMINE PATIENT ID TO USE
    let patientIdToUse: string;
    if (!patientId) {
      if (user.userType !== UserType.PATIENT) {
        throw new BadRequestException('Please send patientId');
      }
      patientIdToUse = user.id;
    } else {
      patientIdToUse = patientId;
    }

    // FIND PATIENT FIRST
    const patientFound = await this.patientRepository.findOne({
      where: { id: patientIdToUse },
      relations: ['profilePicture', 'insuranceDetails'],
    });

    if (!patientFound) {
      throw new BadRequestException('Patient not found');
    }

    // CHECK AUTHORIZATION
    if (user.userType === UserType.PATIENT) {
      // PATIENT CAN ONLY VIEW THEIR OWN DATA
      if (patientFound.id !== user.id) {
        throw new ForbiddenException(
          'You can only view your own patient information',
        );
      }
    } else if (user.userType === UserType.DOCTOR) {
      // DOCTOR CAN VIEW IF THEY HAVE APPOINTMENTS WITH PATIENT OR SHARED EMR ACCESS
      const appointment =
        await this.appointmentService.getDoctorsWithPatientAppointments(
          patientIdToUse,
        );
      const hasAppointments = appointment.some(
        (doctor) => doctor.id === user.id,
      );

      const hasSharedEmrAccess = await this.emrService.hasDoctorSharedEmrAccess(
        user.id,
        patientIdToUse,
      );

      if (!hasAppointments && !hasSharedEmrAccess) {
        throw new ForbiddenException(
          'You can only view patients you have appointments with or whose EMR has been shared with you',
        );
      }
    }

    return patientFound;
  }

  async fetchConcernedPatients(
    user: DoctorWithUserType,
    fetchConcernedPatientsDto?: FetchConcernedPatientsDto,
  ): Promise<{
    patients: Patient[];
    total: number;
    offset: number;
    limit: number;
  }> {
    const {
      searchText,
      offset = 0,
      limit = 20,
    } = fetchConcernedPatientsDto || {};

    // BUILD BASE WHERE CONDITIONS FOR CONCERNED PATIENTS
    let baseWhereConditions = [
      // PATIENTS FROM APPOINTMENTS
      {
        appointments: {
          doctor: { id: user.id },
        },
      },
      // PATIENTS FROM SHARED EMR ACCESS (SHARED BY DOCTOR)
      {
        sharedEmrs: {
          sharedBy: { id: user.id },
        },
      },
      // PATIENTS FROM SHARED EMR ACCESS (SHARED TO DOCTOR)
      {
        sharedEmrs: {
          sharedTo: { id: user.id },
        },
      },
    ];

    // ADD SEARCH CONDITIONS IF SEARCH TEXT PROVIDED
    let finalWhereConditions = [];
    if (searchText) {
      const searchPattern = `%${searchText}%`;
      baseWhereConditions.forEach((baseCondition) => {
        finalWhereConditions.push(
          // SEARCH BY FIRST NAME
          {
            ...baseCondition,
            firstName: ILike(searchPattern),
          },
          // SEARCH BY LAST NAME
          {
            ...baseCondition,
            lastName: ILike(searchPattern),
          },
          // SEARCH BY EMAIL
          {
            ...baseCondition,
            email: ILike(searchPattern),
          },
          // SEARCH BY CONTACT NUMBER
          {
            ...baseCondition,
            contactNumber: ILike(searchPattern),
          },
          // SEARCH BY MEDICAL CONCERNS
          {
            ...baseCondition,
            medicalConcerns: ILike(searchPattern),
          },
        );
      });
    } else {
      finalWhereConditions = baseWhereConditions;
    }

    // GET PATIENTS WITH PAGINATION
    const [patients, total] = await this.patientRepository.findAndCount({
      where: finalWhereConditions,
      relations: ['profilePicture', 'insuranceDetails'],
      skip: offset,
      take: limit,
      order: { createdAt: 'DESC' },
    });

    return {
      patients,
      total,
      offset,
      limit,
    };
  }

  // HELPER FUNCTIONS
  // FINDS PATIENT BY EMAIL WITH ALL RELATIONS
  async findByEmail(email: string): Promise<Patient | undefined> {
    return this.patientRepository.findOne({
      where: { email },
      relations: ['profilePicture', 'insuranceDetails'],
    });
  }
  // FINDS PATIENT BY ID WITH ALL RELATIONS
  async findById(id: string): Promise<Patient | undefined> {
    return this.patientRepository.findOne({
      where: { id },
      relations: ['profilePicture', 'insuranceDetails'],
    });
  }
}
