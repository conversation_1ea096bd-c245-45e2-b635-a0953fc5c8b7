import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { PatientService } from './patient.service';
import { Patient } from './entities/patient.entity';
import { CreatePatientDto } from './dtos/create-patient.dto';
import { AuthenticationGuard } from 'src/auth/guards/authentication.guard';
import { AuthorizationGuard } from 'src/auth/guards/authorization.guard';
import { UserTypes } from 'src/common/decorators/userType.decorator';
import { UserType } from 'src/common/enums/userType.enum';
import { UpdatePatientDto } from './dtos/update-patient.dto';
import { RequestWithUser } from 'src/common/interfaces/requestWithUser.interface';
import { ForgotPasswordPatientDto } from './dtos/forgotPassword-patient.dto';
import { ResetPasswordPatientDto } from './dtos/resetPassword-patient.dto';
import { VerifyForgotPasswordOtpPatientDto } from './dtos/verifyForgotPasswordOtp-patient.dto';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { File } from 'multer';
import { JsonValidatePipe } from 'src/common/pipes/json-validate.pipe';
import { ChangePasswordPatientDto } from './dtos/changePassword-patient.dto';
import { FetchConcernedPatientsDto } from './dtos/fetch-concerned-patients.dto';

@Controller('api/patient')
export class PatientController {
  // CONSTRUCTOR
  constructor(private readonly patientService: PatientService) {}

  //CONTROLLERS HERE
  @Post('signup')
  async createPatient(@Body() createPatientDto: CreatePatientDto) {
    const data = await this.patientService.createPatient(createPatientDto);
    return { message: 'Signup successful', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.PATIENT)
  @UseInterceptors(AnyFilesInterceptor())
  @Put('update')
  async updatePatient(
    @UploadedFiles() files: File[],
    @Body('patientData', new JsonValidatePipe(UpdatePatientDto))
    updatePatientDto: UpdatePatientDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as PatientWithUserType;
    const data = await this.patientService.updatePatient(
      updatePatientDto,
      files,
      user,
    );
    return { message: 'Patient updated successfully', data };
  }

  @Post('requestForgotPassword')
  async requestForgotPatientPassword(
    @Body() forgotPasswordPatientDto: ForgotPasswordPatientDto,
  ) {
    const data = await this.patientService.requestForgotPatientPassword(
      forgotPasswordPatientDto,
    );
    return { message: 'Otp sent to email', data };
  }

  @Post('resetPassword')
  async resetPatientPassword(
    @Body() resetPasswordPatientDto: ResetPasswordPatientDto,
  ) {
    const data = await this.patientService.resetPatientPassword(
      resetPasswordPatientDto,
    );
    return { message: 'Password reset successfully', data };
  }

  @Post('verifyForgotPasswordOtp')
  async verifyForgotPatientPasswordOtp(
    @Body()
    verifyForgotPasswordOtpPatientDto: VerifyForgotPasswordOtpPatientDto,
  ) {
    const data = await this.patientService.verifyForgotPatientPasswordOtp(
      verifyForgotPasswordOtpPatientDto,
    );
    return { message: 'Otp verified', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.PATIENT)
  @Put('changePassword')
  async changePatientPassword(
    @Body() changePasswordPatientDto: ChangePasswordPatientDto,
    @Req() req: RequestWithUser,
  ) {
    const data = await this.patientService.changePatientPassword(
      changePasswordPatientDto,
      req.user as PatientWithUserType,
    );

    return { message: 'Password changed successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('fetchPatientById/:patientId?')
  async fetchPatientById(
    @Param('patientId') patientId: string,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType | PatientWithUserType;
    const data = await this.patientService.fetchPatientById(user, patientId);
    return { message: 'Patient fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Get('fetchConcernedPatients')
  async fetchConcernedPatients(
    @Query() fetchConcernedPatientsDto: FetchConcernedPatientsDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.patientService.fetchConcernedPatients(
      user,
      fetchConcernedPatientsDto,
    );
    return { message: 'Concerned patients fetched successfully', data };
  }
}
