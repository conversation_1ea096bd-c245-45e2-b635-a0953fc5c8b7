// src/auth/auth.module.ts
import { Module, forwardRef } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { DoctorModule } from 'src/doctor/doctor.module';
import { ConfigModule } from '@nestjs/config';
import { AuthenticationGuard } from './guards/authentication.guard';
import { AuthorizationGuard } from './guards/authorization.guard';
import { Reflector } from '@nestjs/core';
import { PatientModule } from 'src/patient/patient.module';

@Module({
  imports: [
    forwardRef(() => DoctorModule),
    forwardRef(() => PatientModule),
    ConfigModule,
  ],
  providers: [AuthService, AuthenticationGuard, AuthorizationGuard, Reflector],
  controllers: [AuthController],
  exports: [AuthService, AuthenticationGuard, AuthorizationGuard],
})
export class AuthModule {}
