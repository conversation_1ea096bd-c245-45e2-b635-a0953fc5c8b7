// src/auth/auth.controller.ts
import { Body, Controller, Get, Post, Req, UseGuards } from '@nestjs/common';
import { AuthService } from './auth.service';
import { LoginDoctorDto } from './dtos/loginDoctor.dto';
import { LoginPatientDto } from './dtos/loginPatient.dto';
import { AuthenticationGuard } from './guards/authentication.guard';
import { RequestWithUser } from 'src/common/interfaces/requestWithUser.interface';

@Controller('api/auth')
export class AuthController {
  // CONSTRUCTOR
  constructor(private readonly authService: AuthService) {}

  // CONTROLLERS
  @Post('loginDoctor')
  async loginDoctor(@Body() loginDoctorDto: LoginDoctorDto) {
    const data = await this.authService.loginDoctor(loginDoctorDto);
    return { message: 'Login successful', data };
  }

  @Post('loginPatient')
  async loginPatient(@Body() loginPatientDto: LoginPatientDto) {
    const data = await this.authService.loginPatient(loginPatientDto);
    return { message: 'Login successful', data };
  }

  @Get('/me')
  @UseGuards(AuthenticationGuard)
  async me(@Req() req: RequestWithUser) {
    return { message: 'User data fetched successfully', data: req.user };
  }
}
