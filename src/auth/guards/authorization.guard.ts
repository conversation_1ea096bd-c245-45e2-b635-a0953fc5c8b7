import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { USER_TYPES_KEY } from 'src/common/decorators/userType.decorator';
import { UserType } from 'src/common/enums/userType.enum';
import { Request } from 'express';
import { RequestWithUser } from 'src/common/interfaces/requestWithUser.interface';

@Injectable()
export class AuthorizationGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredUserTypes = this.reflector.getAllAndOverride<UserType[]>(
      USER_TYPES_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requiredUserTypes) {
      return true; // No user types required, allow access
    }
    const request: RequestWithUser = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    if (!requiredUserTypes.includes(user.userType)) {
      throw new ForbiddenException('Invalid userType');
    }

    return true;
  }
}
