// src/auth/auth.service.ts
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import * as jwt from 'jsonwebtoken';
import { DoctorService } from 'src/doctor/doctor.service';
import { ConfigService } from '@nestjs/config';
import { Doctor } from 'src/doctor/entities/doctor.entity';
import { UserType } from 'src/common/enums/userType.enum';
import { LoginDoctorDto } from './dtos/loginDoctor.dto';
import * as bcrypt from 'bcrypt';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { LoginPatientDto } from './dtos/loginPatient.dto';
import { PatientService } from 'src/patient/patient.service';
import { Patient } from 'src/patient/entities/patient.entity';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';
import { instanceToPlain, plainToInstance } from 'class-transformer';

@Injectable()
export class AuthService {
  // CONSTRUCTOR
  constructor(
    @Inject(forwardRef(() => DoctorService))
    private readonly doctorService: DoctorService,

    @Inject(forwardRef(() => PatientService))
    private readonly patientService: PatientService,

    private readonly configService: ConfigService,
  ) {}

  // SERVICES
  async loginDoctor(loginDoctorDto: LoginDoctorDto) {
    const { email, password } = loginDoctorDto;

    const exisitngDoctor = await this.doctorService.findByEmail(email);

    if (!exisitngDoctor) {
      throw new BadRequestException('Invalid credentials');
    }

    const isPasswordCorrect = await bcrypt.compare(
      password,
      exisitngDoctor.password,
    );

    if (!isPasswordCorrect) {
      throw new BadRequestException('Invalid credentails');
    }

    const token = await this.generateToken_doctor(exisitngDoctor);

    return {
      token,
      user: { ...instanceToPlain(exisitngDoctor), userType: UserType.DOCTOR },
    };
  }
  async loginPatient(loginPatientDto: LoginPatientDto) {
    const { email, password } = loginPatientDto;

    const exisitngPatient = await this.patientService.findByEmail(email);

    if (!exisitngPatient) {
      throw new BadRequestException('Invalid credentials');
    }

    const isPasswordCorrect = await bcrypt.compare(
      password,
      exisitngPatient.password,
    );

    if (!isPasswordCorrect) {
      throw new BadRequestException('Invalid credentails');
    }

    const token = await this.generateToken_patient(exisitngPatient);

    return {
      token,
      user: { ...instanceToPlain(exisitngPatient), userType: UserType.PATIENT },
    };
  }

  // HELPER FUNCTIONS
  // GENERATES JWT TOKEN FOR DOCTOR
  async generateToken_doctor(doctor: Doctor): Promise<string> {
    const payload = {
      sub: doctor.id,
      email: doctor.email,
      userType: UserType.DOCTOR,
    };
    const secret = this.configService.get<string>('JWT_SECRET');
    // TOKEN VALIDITY DURATION
    const expiresIn = '3d';

    return jwt.sign(payload, secret, { expiresIn });
  }

  // GENERATES JWT TOKEN FOR PATIENT
  async generateToken_patient(patient: Patient): Promise<string> {
    const payload = {
      sub: patient.id,
      email: patient.email,
      userType: UserType.PATIENT,
    };
    const secret = this.configService.get<string>('JWT_SECRET');
    // TOKEN VALIDITY DURATION
    const expiresIn = '3d';

    return jwt.sign(payload, secret, { expiresIn });
  }

  // VERIFIES JWT TOKEN AND RETURNS USER WITH USER TYPE
  async verifyToken(
    token: string,
  ): Promise<DoctorWithUserType | PatientWithUserType> {
    const secret = this.configService.get<string>('JWT_SECRET');

    try {
      const decoded = jwt.verify(token, secret) as {
        sub: string;
        email: string;
        userType: UserType;
      };

      if (decoded.userType === UserType.DOCTOR) {
        const doctor = await this.doctorService.findById(decoded.sub);
        if (!doctor) {
          throw new UnauthorizedException('Invalid token: Doctor not found');
        }
        const doctorWithUserType = Object.assign(doctor, {
          userType: UserType.DOCTOR,
        }) as DoctorWithUserType;
        return doctorWithUserType;
      } else if (decoded.userType === UserType.PATIENT) {
        const patient = await this.patientService.findById(decoded.sub);
        if (!patient) {
          throw new UnauthorizedException('Invalid token: Patient not found');
        }
        const patientWithUserType = Object.assign(patient, {
          userType: UserType.PATIENT,
        }) as PatientWithUserType;
        return patientWithUserType;
      } else {
        throw new UnauthorizedException('Invalid userType found in token');
      }
    } catch (error) {
      throw new UnauthorizedException('Invalid or expired token');
    }
  }
}
