// src/app.module.ts
import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DoctorModule } from './doctor/doctor.module';
import { FileModule } from './file/file.module';
import { AuthModule } from './auth/auth.module';
import { DatabaseModule } from './database/database.module';
import { PatientModule } from './patient/patient.module';
import { ConsultationModule } from './consultation/consultation.module';
import { ScheduleModule } from './schedule/schedule.module';
import { AppointmentModule } from './appointment/appointment.module';
import { EmrModule } from './emr/emr.module';
import { VideoCallModule } from './videoCall/videoCall.module';

@Module({
  imports: [
    DatabaseModule,
    DoctorModule,
    FileModule,
    AuthModule,
    PatientModule,
    ConsultationModule,
    ScheduleModule,
    AppointmentModule,
    EmrModule,
    VideoCallModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
