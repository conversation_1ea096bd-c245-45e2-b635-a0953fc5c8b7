// src/consultation/entities/consultation.entity.ts
import { Exclude } from 'class-transformer';
import { Appointment } from 'src/appointment/entities/appointment.entity';
import { Doctor } from 'src/doctor/entities/doctor.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  RelationId,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class Consultation {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  serviceName: string;

  @Column({ nullable: false })
  price: number;

  @Exclude()
  @ManyToOne(() => Doctor, (doctor) => doctor.consultations, {
    onDelete: 'CASCADE',
  })
  doctor: Doctor;

  @RelationId((consultation: Consultation) => consultation.doctor)
  doctorId: string;

  @OneToMany(() => Appointment, (appointment) => appointment.consultation)
  appointments: Appointment[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
