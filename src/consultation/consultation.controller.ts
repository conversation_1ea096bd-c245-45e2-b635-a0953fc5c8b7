// src/consultation/consultation.controller.ts
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ConsultationService } from './consultation.service';
import { CreateConsultationDto } from './dtos/create-consultation.dto';
import { AuthenticationGuard } from 'src/auth/guards/authentication.guard';
import { AuthorizationGuard } from 'src/auth/guards/authorization.guard';
import { UserTypes } from 'src/common/decorators/userType.decorator';
import { UserType } from 'src/common/enums/userType.enum';
import { RequestWithUser } from 'src/common/interfaces/requestWithUser.interface';
import { Doctor } from 'src/doctor/entities/doctor.entity';
import { CreateMultipleConsultationsDto } from './dtos/create-multiple-consultations.dto';
import { UpdateConsultationsDoctorDto } from './dtos/update-consultations-doctor.dto';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';

@Controller('api/consultation')
export class ConsultationController {
  // CONSTRUCTOR
  constructor(private readonly consultationService: ConsultationService) {}

  //  CONTROLLERS HERE
  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Post('create')
  async createConsultation(
    @Body() createConsultationDto: CreateConsultationDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.consultationService.createConsultation(
      user,
      createConsultationDto,
    );

    return { message: 'Consultation added successfully', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Post('createMultiple')
  async createMultipleConsultations(
    @Body() createMultipleDto: CreateMultipleConsultationsDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.consultationService.createMultipleConsultations(
      user,
      createMultipleDto,
    );
    return { message: 'Consultations added successfully', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Put('updateConsultationsDoctor')
  async updateConsultationsDoctor(
    @Body() updateDto: UpdateConsultationsDoctorDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.consultationService.updateConsultationsDoctor(
      user,
      updateDto,
    );
    return { message: 'Consultations updated successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getDoctorConsultations/:doctorId?')
  async getDoctorConsultations(
    @Param('doctorId') doctorId: string,
    @Req() req: RequestWithUser,
  ) {
    const data = await this.consultationService.getConsultationsOfDoctor(
      req.user,
      doctorId,
    );
    return {
      message: 'Consultations fetched successfully',
      data,
    };
  }
}
