// src/consultation/consultation.service.ts
import {
  BadRequestException,
  Inject,
  Injectable,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Consultation } from './entities/consultation.entity';
import { Repository } from 'typeorm';
import { CreateConsultationDto } from './dtos/create-consultation.dto';
import { Doctor } from 'src/doctor/entities/doctor.entity';
import { CreateMultipleConsultationsDto } from './dtos/create-multiple-consultations.dto';
import { UpdateConsultationsDoctorDto } from './dtos/update-consultations-doctor.dto';
import { UserType } from 'src/common/enums/userType.enum';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';
import { DoctorService } from 'src/doctor/doctor.service';

@Injectable()
export class ConsultationService {
  // CONSTRUCTOR
  constructor(
    @InjectRepository(Consultation)
    private readonly consultationRepository: Repository<Consultation>,

    @Inject(forwardRef(() => DoctorService))
    private readonly doctorService: DoctorService,
  ) {}

  // SERVICES
  async createConsultation(
    user: DoctorWithUserType,
    createConsultationDto: CreateConsultationDto,
  ): Promise<Consultation[]> {
    const { doctorId, price, serviceName } = createConsultationDto;

    // SELF CHECK
    const doctorIdToUse = doctorId ? doctorId : user.id;

    if (doctorId && doctorId !== user.id) {
      throw new BadRequestException(
        'You cannot add consultations for other doctors',
      );
    }

    // CHECK DOCTOR
    const doctor = await this.doctorService.findById(doctorIdToUse);

    if (!doctor) {
      throw new BadRequestException('No doctor found against this id');
    }

    // SAVE CONSULTATION
    await this.consultationRepository.save({
      serviceName,
      price,
      doctor,
    });

    // RETURN ALL CONSULTATIONS OF THIS DOCTOR
    const allConsultationsOfDoctor = await this.consultationRepository.find({
      where: { doctor: { id: doctor.id } },
    });

    return allConsultationsOfDoctor;
  }
  async createMultipleConsultations(
    user: DoctorWithUserType,
    createMultipleDto: CreateMultipleConsultationsDto,
  ): Promise<Consultation[]> {
    const { doctorId, consultations } = createMultipleDto;
    const doctorIdToUse = doctorId ? doctorId : user.id;

    // SELF CHECK
    if (doctorId && doctorId !== user.id) {
      throw new BadRequestException(
        'You cannot add consultations for other doctors',
      );
    }

    // DOCTOR CHECK
    const doctor = await this.doctorService.findById(doctorIdToUse);

    if (!doctor) {
      throw new BadRequestException('No doctor found for this id');
    }

    // SAVE CONSULATIONS
    const consultationEntities = consultations.map((dto) =>
      this.consultationRepository.create({
        serviceName: dto.serviceName,
        price: dto.price,
        doctor,
      }),
    );

    await this.consultationRepository.save(consultationEntities);

    // RETURN ALL CONSULTATIONS OF THIS DOCTOR
    const allConsultationsOfDoctor = await this.consultationRepository.find({
      where: { doctor: { id: doctor.id } },
    });

    return allConsultationsOfDoctor;
  }
  async updateConsultationsDoctor(
    user: DoctorWithUserType,
    updateDto: UpdateConsultationsDoctorDto,
  ): Promise<Consultation[]> {
    const { doctorId, consultations } = updateDto;
    const doctorIdToUse = doctorId ? doctorId : user.id;

    // SELF CHECK
    if (doctorId && doctorId !== user.id) {
      throw new BadRequestException(
        'You cannot update consultations for other doctors',
      );
    }

    const doctor = await this.doctorService.findById(doctorIdToUse);

    if (!doctor) {
      throw new BadRequestException('No doctor found for this id');
    }

    // REMOVE PREVIOUS
    const consultationsToRemove = await this.consultationRepository.find({
      where: { doctor: { id: doctor.id } },
      relations: ['doctor'],
    });
    await this.consultationRepository.remove(consultationsToRemove);

    // SAVE NEW CONSULTATIONS
    const newConsultations = consultations.map((dto) =>
      this.consultationRepository.create({
        serviceName: dto.serviceName,
        price: dto.price,
        doctor,
      }),
    );

    await this.consultationRepository.save(newConsultations);

    // RETURN ALL CONSULTATIONS OF THIS DOCTOR
    const allConsultationsOfDoctor = await this.consultationRepository.find({
      where: { doctor: { id: doctor.id } },
    });

    return allConsultationsOfDoctor;
  }
  async getConsultationsOfDoctor(
    user: DoctorWithUserType | PatientWithUserType,
    doctorId?: string,
  ): Promise<Consultation[]> {
    // SELF CHECK
    let doctorIdToUse: string;
    if (!doctorId) {
      if (user.userType !== UserType.DOCTOR) {
        throw new BadRequestException(
          'Only a doctor can fetch their own consultations when doctorId is not provided.',
        );
      }
      doctorIdToUse = user.id;
    } else {
      doctorIdToUse = doctorId;
    }

    // FETCH CONSULTATIONS
    const consultations = await this.consultationRepository.find({
      where: { doctor: { id: doctorIdToUse } },
    });

    return consultations;
  }

  // HELPER FUNCTIONS
  // FINDS CONSULTATION BY ID
  async findById(id: string): Promise<Consultation | undefined> {
    return this.consultationRepository.findOne({
      where: { id },
    });
  }
}
