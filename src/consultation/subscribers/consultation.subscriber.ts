// consultation/subscribers/consultation.subscriber.ts
import {
  EventSubscriber,
  EntitySubscriberInterface,
  InsertEvent,
  UpdateEvent,
  RemoveEvent,
} from 'typeorm';
import { Consultation } from 'src/consultation/entities/consultation.entity';
import { Doctor } from 'src/doctor/entities/doctor.entity';

@EventSubscriber()
export class ConsultationSubscriber
  implements EntitySubscriberInterface<Consultation>
{
  listenTo() {
    return Consultation;
  }

  async afterInsert(event: InsertEvent<Consultation>) {
    console.log('INSERT CONSULTATION SUBSCRIBER');
    if (event?.entity?.doctorId) {
      await this.updateDoctorProfileCompleteness(
        event.manager,
        event?.entity?.doctorId,
      );
    }
  }

  async afterUpdate(event: UpdateEvent<Consultation>) {
    console.log('UPDATE CONSULTATION SUBSCRIBER');
    if (event?.databaseEntity?.doctorId) {
      await this.updateDoctorProfileCompleteness(
        event.manager,
        event?.databaseEntity?.doctorId,
      );
    }
  }

  async afterRemove(event: RemoveEvent<Consultation>) {
    console.log('DELETE CONSULTATION SUBSCRIBER');
    if (event.databaseEntity?.doctorId) {
      console.log('RUNNING UPDATE CHECK');
      await this.updateDoctorProfileCompleteness(
        event.manager,
        event.databaseEntity.doctorId,
      );
    }
  }

  private async updateDoctorProfileCompleteness(
    manager: any,
    doctorId: string,
  ) {
    const doctor = await manager.findOne(Doctor, {
      where: { id: doctorId },
      relations: [
        'qualifications',
        'practiceExperiences',
        'consultations',
        'profilePicture',
        'signature',
        'idFront',
        'idBack',
      ],
    });

    if (!doctor) return;

    doctor.isProfileComplete = !!(
      doctor.email &&
      doctor.firstName &&
      doctor.lastName &&
      doctor.contactNumber &&
      doctor.gender &&
      doctor.dob &&
      doctor.address &&
      doctor.language &&
      doctor.country &&
      doctor.bio &&
      doctor.speciality &&
      doctor.speciality.length > 0 &&
      doctor.yearsOfExperience !== null &&
      doctor.yearsOfExperience !== undefined &&
      doctor.medicalLicenseNumber &&
      doctor.qualifications &&
      doctor.qualifications.length > 0 &&
      doctor.practiceExperiences &&
      doctor.practiceExperiences.length > 0 &&
      doctor.consultations &&
      doctor.consultations.length > 0 &&
      doctor.profilePicture &&
      doctor.profilePicture.url &&
      doctor.signature &&
      doctor.signature.url &&
      doctor.idFront &&
      doctor.idFront.url &&
      doctor.idBack &&
      doctor.idBack.url
    );

    await manager.save(Doctor, doctor, { listeners: false });
  }
}
