import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export class SingleConsultationDto {
  @IsNotEmpty()
  @IsString()
  serviceName: string;

  @IsNotEmpty()
  @IsNumber()
  @IsPositive()
  price: number;
}

export class CreateMultipleConsultationsDto {
  @IsOptional()
  @IsString()
  doctorId?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SingleConsultationDto)
  consultations: SingleConsultationDto[];
}
