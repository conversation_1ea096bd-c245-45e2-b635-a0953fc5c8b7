import {
  IsArray,
  IsOptional,
  IsString,
  IsNotEmpty,
  IsNumber,
  IsPositive,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export class SingleConsultationDto {
  @IsNotEmpty()
  @IsString()
  serviceName: string;

  @IsNotEmpty()
  @IsNumber()
  @IsPositive()
  price: number;
}

export class UpdateConsultationsDoctorDto {
  @IsOptional()
  @IsString()
  doctorId?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SingleConsultationDto)
  consultations: SingleConsultationDto[];
}
