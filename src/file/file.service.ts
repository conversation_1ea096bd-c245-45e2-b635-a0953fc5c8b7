// src/file/file.service.ts
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { File } from './entities/file.entity';
import { File as MulterFile } from 'multer';
import { S3, PutObjectCommand } from '@aws-sdk/client-s3';

@Injectable()
export class FileService {
  // GLOBAL FILE VARIABLE
  private s3: S3;

  // CONSTRUCTOR
  constructor(
    @InjectRepository(File)
    private readonly fileRepository: Repository<File>,
  ) {
    this.s3 = new S3({
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
      },
      region: process.env.AWS_REGION,
    });
  }

  //  SERVICES
  async uploadFile(
    file: MulterFile,
    ownerType: string,
    ownerId: string,
  ): Promise<File> {
    console.log('FILE UPLOAD START');

    const sanitizedFilename = file.originalname.replace(/\s+/g, '');

    // UNIQUE NAME
    const key = `uploads/${ownerType}/${Date.now()}`;

    console.log('KEY => ', key);

    // UPLOAD TO S3
    const result = await this.s3.send(
      new PutObjectCommand({
        Bucket: process.env.AWS_BUCKET,
        Key: key,
        Body: file.buffer,
        ContentType: file.mimetype,
      }),
    );

    // MAKE URL
    const url = `https://${process.env.AWS_BUCKET}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;

    // CREATE A NEW FILE IN DB
    const newFile = new File();
    newFile.name = file.originalname;
    newFile.path = key;
    newFile.mimeType = file.mimetype;
    newFile.size = file.size;
    newFile.url = url;
    newFile.ownerId = ownerId;
    newFile.ownerType = ownerType;

    return await this.fileRepository.save(newFile);
  }
  async createFileFromUrl(
    url: string,
    ownerType: string,
    ownerId: string,
  ): Promise<File> {
    const file = new File();
    file.name = ''; // NOT AVAILABLE FOR URL FILE
    file.path = ''; // NOT AVAILABLE FOR URL FILE
    file.url = url;
    file.ownerId = ownerId;
    file.ownerType = ownerType;

    return await this.fileRepository.save(file);
  }
}
