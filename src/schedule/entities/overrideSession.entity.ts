// src/schedule/entities/overrideSession.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { OverrideSchedule } from './overrideSchedule.entity';

@Entity()
export class OverrideSession {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => OverrideSchedule, (override) => override.sessions, {
    onDelete: 'CASCADE',
  })
  overrideSchedule: OverrideSchedule;

  @Column({ type: 'time' })
  startTime: string; // EXAMPLE : "10:00:00"

  @Column({ type: 'time' })
  endTime: string; // EXAMPLE : "10:00:00"

  @Column('int')
  slotDuration: number; // EXAMPLE : 30

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
