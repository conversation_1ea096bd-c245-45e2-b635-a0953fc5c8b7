// weekly-session.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { WeeklySchedule } from './weeklySchedule.entity';

@Entity()
export class WeeklySession {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => WeeklySchedule, (schedule) => schedule.sessions, {
    onDelete: 'CASCADE',
  })
  weeklySchedule: WeeklySchedule;

  @Column({ type: 'time' })
  startTime: string; // e.g., "09:00:00"

  @Column({ type: 'time' })
  endTime: string; // e.g., "13:00:00"

  @Column('int')
  slotDuration: number; // in minutes (e.g., 15)

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
