// schedule-override.entity.ts
import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Doctor } from 'src/doctor/entities/doctor.entity';
import { OverrideSession } from './overrideSession.entity';

@Entity()
export class OverrideSchedule {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Doctor, (doctor) => doctor.overrideSchedule, {
    onDelete: 'CASCADE',
  })
  doctor: Doctor;

  @Column({ type: 'date' })
  date: Date;

  @OneToMany(() => OverrideSession, (session) => session.overrideSchedule, {
    cascade: true,
  })
  sessions: OverrideSession[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
