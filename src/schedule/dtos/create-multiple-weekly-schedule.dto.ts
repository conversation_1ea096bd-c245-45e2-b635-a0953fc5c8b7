import {
  IsO<PERSON>al,
  IsString,
  IsNotEmpty,
  IsArray,
  ValidateNested,
  IsEnum,
  ArrayMinSize,
} from 'class-validator';
import { Type } from 'class-transformer';
import { DayOfWeek } from '../entities/weeklySchedule.entity';
import { WeeklySessionDto } from './create-weekly-schedule.dto';

class MultipleWeeklyScheduleItem {
  @IsNotEmpty()
  @IsEnum(DayOfWeek)
  dayOfWeek: DayOfWeek;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WeeklySessionDto)
  sessions: WeeklySessionDto[];
}

export class CreateMultipleWeeklyScheduleDto {
  @IsOptional()
  @IsString()
  doctorId?: string;

  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => MultipleWeeklyScheduleItem)
  schedules: MultipleWeeklyScheduleItem[];
}
