import {
  IsOptional,
  IsString,
  IsNotEmpty,
  IsEnum,
  IsArray,
  ValidateNested,
  IsInt,
  Min,
  IsMilitaryTime,
  ArrayMinSize,
} from 'class-validator';
import { Type } from 'class-transformer';
import { DayOfWeek } from '../entities/weeklySchedule.entity';

//  DTO for a single weekly session
export class WeeklySessionDto {
  @IsNotEmpty()
  @IsMilitaryTime()
  @IsString()
  startTime: string; // e.g. "09:00"

  @IsNotEmpty()
  @IsMilitaryTime()
  @IsString()
  endTime: string; // e.g. "13:00"

  @IsNotEmpty()
  @IsInt()
  @Min(15)
  slotDuration: number; // e.g. 15
}

/**
 * DTO for creating a single day's weekly schedule
 */
export class CreateWeeklyScheduleDto {
  @IsOptional()
  @IsString()
  doctorId?: string;

  @IsNotEmpty()
  @IsEnum(DayOfWeek)
  dayOfWeek: DayOfWeek;

  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => WeeklySessionDto)
  sessions: WeeklySessionDto[];
}
