// src/schedule/schedule.module.ts
import { Module, forwardRef } from '@nestjs/common';
import { ScheduleService } from './schedule.service';
import { ScheduleController } from './schedule.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OverrideSchedule } from './entities/overrideSchedule.entity';
import { OverrideSession } from './entities/overrideSession.entity';
import { WeeklySchedule } from './entities/weeklySchedule.entity';
import { WeeklySession } from './entities/weeklySession.entity';

import { AuthModule } from 'src/auth/auth.module';
import { DoctorModule } from 'src/doctor/doctor.module';
import { AppointmentModule } from 'src/appointment/appointment.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      OverrideSchedule,
      OverrideSession,
      WeeklySchedule,
      WeeklySession,
    ]),
    forwardRef(() => AuthModule),
    forwardRef(() => DoctorModule),
    forwardRef(() => AppointmentModule),
  ],
  controllers: [ScheduleController],
  providers: [ScheduleService],
  exports: [ScheduleService],
})
export class ScheduleModule {}
