import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ScheduleService } from './schedule.service';
import { CreateWeeklyScheduleDto } from './dtos/create-weekly-schedule.dto';
import { CreateMultipleWeeklyScheduleDto } from './dtos/create-multiple-weekly-schedule.dto';
import { CreateOverrideScheduleDto } from './dtos/create-override-schedule.dto';
import { CreateMultipleOverrideScheduleDto } from './dtos/create-multiple-override-schedule.dto';
import { UpdateWeeklyScheduleDto } from './dtos/update-weekly-schedule.dto';
import { UpdateOverrideScheduleDto } from './dtos/update-override-schedule.dto';
import { AuthenticationGuard } from 'src/auth/guards/authentication.guard';
import { AuthorizationGuard } from 'src/auth/guards/authorization.guard';
import { UserTypes } from 'src/common/decorators/userType.decorator';
import { UserType } from 'src/common/enums/userType.enum';
import { RequestWithUser } from 'src/common/interfaces/requestWithUser.interface';
import { Doctor } from 'src/doctor/entities/doctor.entity';
import { GetSlotDetailsDto } from './dtos/get-slot-details.dto';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';

@Controller('api/schedule')
export class ScheduleController {
  // CONSTRUCTOR
  constructor(private readonly scheduleService: ScheduleService) {}

  // CONTROLLERS HERE
  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Post('createWeeklySingleDay')
  async createWeeklyScheduleForSingleDay(
    @Body() createWeeklyDto: CreateWeeklyScheduleDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.scheduleService.createWeeklyScheduleForSingleDay(
      user,
      createWeeklyDto,
    );
    return {
      message: 'Weekly schedule created successfully for single day',
      data,
    };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Post('createWeeklyMultipleDays')
  async createMultipleWeeklySchedules(
    @Body() createMultipleWeeklyDto: CreateMultipleWeeklyScheduleDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as Doctor;
    const data = await this.scheduleService.createMultipleWeeklySchedules(
      user,
      createMultipleWeeklyDto,
    );
    return {
      message: 'Weekly schedules created successfully for multiple days',
      data,
    };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Post('createOverrideSingleDate')
  async createOverrideSchedule(
    @Body() createOverrideDto: CreateOverrideScheduleDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as Doctor;
    const data = await this.scheduleService.createOverrideSchedule(
      user,
      createOverrideDto,
    );
    return {
      message: 'Override schedule created successfully for single date',
      data,
    };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Post('createOverrideMultipleDates')
  async createMultipleOverrideSchedules(
    @Body() createMultipleOverrideDto: CreateMultipleOverrideScheduleDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as Doctor;
    const data = await this.scheduleService.createMultipleOverrideSchedules(
      user,
      createMultipleOverrideDto,
    );
    return {
      message: 'Override schedules created successfully for multiple dates',
      data,
    };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Put('updateWeeklyScheduleDoctor')
  async updateWeeklySchedulesDoctor(
    @Body() updateWeeklyDto: UpdateWeeklyScheduleDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as Doctor;
    const data = await this.scheduleService.updateWeeklySchedulesDoctor(
      user,
      updateWeeklyDto,
    );
    return { message: 'Weekly schedule updated successfully', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Put('updateOverrideScheduleDoctor')
  async updateOverrideSchedulesDoctor(
    @Body() updateOverrideDto: UpdateOverrideScheduleDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as Doctor;
    const data = await this.scheduleService.updateOverrideSchedulesDoctor(
      user,
      updateOverrideDto,
    );
    return { message: 'Override schedule updated successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getDoctorSlotDetails/:doctorId?')
  async getDoctorSlotDetails(
    @Param('doctorId') doctorId: string,
    @Query() getSlotDetailsDto: GetSlotDetailsDto,
    @Req() req: RequestWithUser,
  ) {
    const data = await this.scheduleService.getDoctorSlotDetails(
      req.user,
      doctorId,
      getSlotDetailsDto,
    );
    return { message: 'Slot details fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getWeeklyScheduleOfDoctor/:doctorId?')
  async getWeeklyScheduleOfDoctor(
    @Param('doctorId') doctorId: string,
    @Req() req: RequestWithUser,
  ) {
    const data = await this.scheduleService.getWeeklyScheduleOfDoctor(
      req.user,
      doctorId,
    );
    return { message: 'Weekly schedule fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('getOverrideScheduleOfDoctor/:doctorId?')
  async getOverrideScheduleOfDoctor(
    @Param('doctorId') doctorId: string,
    @Req() req: RequestWithUser,
  ) {
    const data = await this.scheduleService.getOverrideScheduleOfDoctor(
      req.user,
      doctorId,
    );
    return { message: 'Override schedule fetched successfully', data };
  }
}
