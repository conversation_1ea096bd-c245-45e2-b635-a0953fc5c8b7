// src/videoCall/videoCall.controller.ts
import {
  Body,
  Controller,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { VideoCallService } from './videoCall.service';
import { JoinCallDoctorDto } from './dtos/join-call-doctor.dto';
import { JoinCallPatientDto } from './dtos/join-call-patient.dto';
import { AuthenticationGuard } from 'src/auth/guards/authentication.guard';
import { AuthorizationGuard } from 'src/auth/guards/authorization.guard';
import { UserTypes } from 'src/common/decorators/userType.decorator';
import { UserType } from 'src/common/enums/userType.enum';
import { RequestWithUser } from 'src/common/interfaces/requestWithUser.interface';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';

@Controller('api/videoCall')
export class VideoCallController {
  // CONSTRUCTOR
  constructor(private readonly videoCallService: VideoCallService) {}

  // CONTROLLERS
  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Post('joinCallDoctor')
  async joinCallDoctor(
    @Body() joinCallDoctorDto: JoinCallDoctorDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;
    const data = await this.videoCallService.joinCallDoctor(
      user,
      joinCallDoctorDto,
    );
    return { message: 'Doctor video call token generated successfully', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.PATIENT)
  @Post('joinCallPatient')
  async joinCallPatient(
    @Body() joinCallPatientDto: JoinCallPatientDto,
    @Req() req: RequestWithUser,
  ) {
    const user = req.user as PatientWithUserType;
    const data = await this.videoCallService.joinCallPatient(
      user,
      joinCallPatientDto,
    );
    return { message: 'Patient video call token generated successfully', data };
  }
}
