// src/videoCall/videoCall.service.ts
import {
  Injectable,
  BadRequestException,
  ForbiddenException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { VideoCall } from './entities/videoCall.entity';
import { JoinCallDoctorDto } from './dtos/join-call-doctor.dto';
import { JoinCallPatientDto } from './dtos/join-call-patient.dto';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';
import { AppointmentService } from 'src/appointment/appointment.service';
import * as moment from 'moment';
import * as twilio from 'twilio';

@Injectable()
export class VideoCallService {
  private twilioClient: twilio.Twilio;

  // CONSTRUCTOR
  constructor(
    @InjectRepository(VideoCall)
    private readonly videoCallRepository: Repository<VideoCall>,

    @Inject(forwardRef(() => AppointmentService))
    private readonly appointmentService: AppointmentService,
  ) {
    // INITIALIZE TWILIO CLIENT
    this.twilioClient = twilio(
      process.env.TWILIO_ACCOUNT_SID!,
      process.env.TWILIO_AUTH_TOKEN!,
    );
  }

  // SERVICES
  async joinCallDoctor(
    user: DoctorWithUserType,
    joinCallDoctorDto: JoinCallDoctorDto,
  ): Promise<{ token: string }> {
    const { appointmentId } = joinCallDoctorDto;

    // GET APPOINTMENT WITH RELATIONS
    const appointment = await this.appointmentService.getAppointmentById(
      user,
      appointmentId,
    );

    // CHECK IF CURRENT TIME IS WITHIN APPOINTMENT WINDOW
    const now = moment();
    const startTime = moment(appointment.startDateTime);
    const endTime = moment(appointment.endDateTime);

    if (!now.isBetween(startTime, endTime, null, '[]')) {
      throw new BadRequestException(
        'Video call can only be joined during the appointment time',
      );
    }

    // CHECK IF USER IS THE BOOKED DOCTOR
    if (appointment.doctor.id !== user.id) {
      throw new ForbiddenException(
        'Only the booked doctor can join this video call',
      );
    }

    // CHECK IF VIDEOCALL RECORD EXISTS
    let videoCall = await this.videoCallRepository.findOne({
      where: { appointment: { id: appointmentId } },
      relations: ['appointment'],
    });

    // CREATE VIDEOCALL RECORD IF IT DOESN'T EXIST
    if (!videoCall) {
      videoCall = this.videoCallRepository.create({
        appointment,
      });
    }

    // GENERATE DOCTOR TOKEN IF NOT EXISTS
    if (!videoCall.doctorToken) {
      videoCall.doctorToken = this.generateVideoAccessToken(
        `doctor_${user.id}`,
        `appointment_${appointmentId}`,
      );
    }

    // SAVE VIDEOCALL RECORD
    await this.videoCallRepository.save(videoCall);

    return { token: videoCall.doctorToken };
  }
  async joinCallPatient(
    user: PatientWithUserType,
    joinCallPatientDto: JoinCallPatientDto,
  ): Promise<{ token: string }> {
    const { appointmentId } = joinCallPatientDto;

    // GET APPOINTMENT WITH RELATIONS
    const appointment = await this.appointmentService.getAppointmentById(
      user,
      appointmentId,
    );

    // CHECK IF CURRENT TIME IS WITHIN APPOINTMENT WINDOW
    const now = moment();
    const startTime = moment(appointment.startDateTime);
    const endTime = moment(appointment.endDateTime);

    if (!now.isBetween(startTime, endTime, null, '[]')) {
      throw new BadRequestException(
        'Video call can only be joined during the appointment time',
      );
    }

    // CHECK IF USER IS THE CONCERNED PATIENT
    if (appointment.patient.id !== user.id) {
      throw new ForbiddenException(
        'Only the concerned patient can join this video call',
      );
    }

    // CHECK IF VIDEOCALL RECORD EXISTS
    let videoCall = await this.videoCallRepository.findOne({
      where: { appointment: { id: appointmentId } },
      relations: ['appointment'],
    });

    // CREATE VIDEOCALL RECORD IF IT DOESN'T EXIST
    if (!videoCall) {
      videoCall = this.videoCallRepository.create({
        appointment,
      });
    }

    // GENERATE PATIENT TOKEN IF NOT EXISTS
    if (!videoCall.patientToken) {
      videoCall.patientToken = this.generateVideoAccessToken(
        `patient_${user.id}`,
        `appointment_${appointmentId}`,
      );
    }

    // SAVE VIDEOCALL RECORD
    await this.videoCallRepository.save(videoCall);

    return { token: videoCall.patientToken };
  }

  // HELPER FUNCTIONS
  // GENERATES TWILIO VIDEO ACCESS TOKEN FOR VIDEO CALLS
  private generateVideoAccessToken(identity: string, roomName: string): string {
    const accessToken = new twilio.jwt.AccessToken(
      process.env.TWILIO_ACCOUNT_SID!,
      process.env.TWILIO_API_KEY!,
      process.env.TWILIO_API_SECRET!,
      { identity },
    );

    const videoGrant = new twilio.jwt.AccessToken.VideoGrant({
      room: roomName,
    });

    accessToken.addGrant(videoGrant);
    return accessToken.toJwt();
  }
}
