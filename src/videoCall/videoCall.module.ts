// src/videoCall/videoCall.module.ts
import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VideoCall } from './entities/videoCall.entity';
import { VideoCallService } from './videoCall.service';
import { VideoCallController } from './videoCall.controller';
import { AuthModule } from 'src/auth/auth.module';
import { AppointmentModule } from 'src/appointment/appointment.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([VideoCall]),
    forwardRef(() => AuthModule),
    forwardRef(() => AppointmentModule),
  ],
  controllers: [VideoCallController],
  providers: [VideoCallService],
  exports: [VideoCallService],
})
export class VideoCallModule {}
