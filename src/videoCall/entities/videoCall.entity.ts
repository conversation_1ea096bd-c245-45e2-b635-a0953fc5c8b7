// src/videoCall/entities/videoCall.entity.ts
import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { Appointment } from 'src/appointment/entities/appointment.entity';

@Entity()
export class VideoCall {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @OneToOne(() => Appointment, (appointment) => appointment.videoCall, {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  appointment: Appointment;

  @Column({ nullable: true })
  doctorToken: string;

  @Column({ nullable: true })
  patientToken: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
