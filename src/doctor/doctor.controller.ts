import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { DoctorService } from './doctor.service';
import { Doctor } from './entities/doctor.entity';
import { CreateDoctorDto } from './dtos/create-doctor.dto';
import { AuthenticationGuard } from 'src/auth/guards/authentication.guard';
import { AuthorizationGuard } from 'src/auth/guards/authorization.guard';
import { UserTypes } from 'src/common/decorators/userType.decorator';
import { UserType } from 'src/common/enums/userType.enum';
import { UpdateDoctorDto } from './dtos/update-doctor.dto';
import { RequestWithUser } from 'src/common/interfaces/requestWithUser.interface';
import { ForgotPasswordDoctorDto } from './dtos/forgotPassword-doctor.dto';
import { ResetPasswordDoctorDto } from './dtos/resetPassword-doctor.dto';
import { VerifyForgotPasswordOtpDoctorDto } from './dtos/verifyForgotPasswordOtp-doctor.dto';
import {
  AnyFilesInterceptor,
  FileFieldsInterceptor,
} from '@nestjs/platform-express';
import { File } from 'multer';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { FetchDoctorsDto } from './dtos/fetch-doctors.dto';
import { JsonValidatePipe } from 'src/common/pipes/json-validate.pipe';
import { ChangePasswordDoctorDto } from './dtos/changePassword-doctor.dto';

@Controller('api/doctor')
export class DoctorController {
  // CONSTRUCTOR
  constructor(private readonly doctorService: DoctorService) {}

  // CONTROLLERS HERE
  @Post('signup')
  async createDoctor(@Body() createDoctorDto: CreateDoctorDto) {
    const data = await this.doctorService.createDoctor(createDoctorDto);

    return { message: 'Signup successful', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @UseInterceptors(AnyFilesInterceptor())
  @Put('update')
  async updateDoctor(
    @UploadedFiles() files: File[],
    @Body('doctorData', new JsonValidatePipe(UpdateDoctorDto))
    updateDoctorDto: UpdateDoctorDto,
    @Req()
    req: RequestWithUser,
  ) {
    const user = req.user as DoctorWithUserType;

    const data = await this.doctorService.updateDoctor(
      updateDoctorDto,
      files,
      user,
    );

    return { message: 'Doctor updated successfully', data };
  }

  @Post('requestForgotPassword')
  async requestForgotDoctorPassword(
    @Body() forgotPasswordDoctorDto: ForgotPasswordDoctorDto,
  ) {
    const data = await this.doctorService.requestForgotDoctorPassword(
      forgotPasswordDoctorDto,
    );

    return { message: 'Otp sent to email', data };
  }

  @Post('resetPassword')
  async resetDoctorPassword(
    @Body() resetPasswordDoctorDto: ResetPasswordDoctorDto,
  ) {
    const data = await this.doctorService.resetDoctorPassword(
      resetPasswordDoctorDto,
    );

    return { message: 'Password reset successfully', data };
  }

  @Post('verifyForgotPasswordOtp')
  async verifyForgotPasswordOtp(
    @Body() verifyForgotPasswordOtpDoctorDto: VerifyForgotPasswordOtpDoctorDto,
  ) {
    const data = await this.doctorService.verifyForgotPasswordOtp(
      verifyForgotPasswordOtpDoctorDto,
    );

    return { message: 'Otp verified', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('fetchDoctors')
  async fetchDoctors(
    @Query() fetchDoctorsDto: FetchDoctorsDto,
    @Req() req: RequestWithUser,
  ) {
    const data = await this.doctorService.fetchDoctors(
      req.user,
      fetchDoctorsDto,
    );
    return { message: 'Doctors fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard)
  @Get('fetchDoctorById/:doctorId?')
  async fetchDoctorById(
    @Param('doctorId') doctorId: string,
    @Req() req: RequestWithUser,
  ) {
    const data = await this.doctorService.fetchDoctorById(req.user, doctorId);
    return { message: 'Doctor fetched successfully', data };
  }

  @UseGuards(AuthenticationGuard, AuthorizationGuard)
  @UserTypes(UserType.DOCTOR)
  @Put('changePassword')
  async changeDoctorPassword(
    @Body() changePasswordDoctorDto: ChangePasswordDoctorDto,
    @Req() req: RequestWithUser,
  ) {
    const data = await this.doctorService.changeDoctorPassword(
      changePasswordDoctorDto,
      req.user as DoctorWithUserType,
    );

    return { message: 'Password changed successfully', data };
  }
}
