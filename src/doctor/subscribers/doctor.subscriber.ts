// doctor/subscribers/doctor.subscriber.ts
import {
  EventSubscriber,
  EntitySubscriberInterface,
  InsertEvent,
  UpdateEvent,
} from 'typeorm';
import { Doctor } from 'src/doctor/entities/doctor.entity';

@EventSubscriber()
export class DoctorSubscriber implements EntitySubscriberInterface<Doctor> {
  listenTo() {
    return Doctor;
  }

  async afterInsert(event: InsertEvent<Doctor>) {
    console.log('INSERT DOCTOR SUBSCRIBER');
    await this.updateProfileCompleteness(event.manager, event.entity.id);
  }

  async afterUpdate(event: UpdateEvent<Doctor>) {
    console.log('UPDATE DOCTOR SUBSCRIBER');
    if (event.entity) {
      await this.updateProfileCompleteness(event.manager, event.entity.id);
    }
  }

  private async updateProfileCompleteness(manager: any, doctorId: string) {
    const doctor = await manager.findOne(Doctor, {
      where: { id: doctorId },
      relations: [
        'qualifications',
        'practiceExperiences',
        'consultations',
        'profilePicture',
        'signature',
        'idFront',
        'idBack',
      ],
    });
    if (!doctor) return;

    doctor.isProfileComplete = !!(
      doctor.email &&
      doctor.firstName &&
      doctor.lastName &&
      doctor.contactNumber &&
      doctor.gender &&
      doctor.dob &&
      doctor.address &&
      doctor.language &&
      doctor.country &&
      doctor.bio &&
      doctor.speciality &&
      doctor.speciality.length > 0 &&
      doctor.yearsOfExperience !== null &&
      doctor.yearsOfExperience !== undefined &&
      doctor.medicalLicenseNumber &&
      doctor.qualifications &&
      doctor.qualifications.length > 0 &&
      doctor.practiceExperiences &&
      doctor.practiceExperiences.length > 0 &&
      doctor.consultations &&
      doctor.consultations.length > 0 &&
      doctor.profilePicture &&
      doctor.profilePicture.url &&
      doctor.signature &&
      doctor.signature.url &&
      doctor.idFront &&
      doctor.idFront.url &&
      doctor.idBack &&
      doctor.idBack.url
    );

    await manager.save(Doctor, doctor, { listeners: false });
  }
}
