import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Doctor } from './doctor.entity';
import { File } from '../../file/entities/file.entity';
import { Exclude } from 'class-transformer';

@Entity()
export class PracticeExperience {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  hospitalName: string;

  @Column()
  designation: string;

  @Column({ nullable: true })
  description: string;

  @Column({ type: 'date' })
  startDate: Date;

  @Column({ type: 'date' })
  endDate: Date;

  @Exclude()
  @ManyToOne(() => Doctor, (doctor) => doctor.practiceExperiences, {
    onDelete: 'CASCADE',
  })
  doctor: Doctor;

  @OneToOne(() => File, { nullable: false })
  @JoinColumn()
  experienceFile: File;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
