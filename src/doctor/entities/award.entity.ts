import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Doctor } from './doctor.entity';
import { File } from '../../file/entities/file.entity';
import { Exclude } from 'class-transformer';

@Entity()
export class Award {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  awardName: string;

  @Column({ type: 'date' })
  issueDate: Date;

  @Column()
  awardedBy: string;

  @Column()
  awardDetails: string;

  @Exclude()
  @ManyToOne(() => Doctor, (doctor) => doctor.awards, {
    onDelete: 'CASCADE',
  })
  doctor: Doctor;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
