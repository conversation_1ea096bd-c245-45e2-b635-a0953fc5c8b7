import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Doctor } from './doctor.entity';
import { File } from '../../file/entities/file.entity';
import { Exclude } from 'class-transformer';

@Entity()
export class Qualification {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  institutionName: string;

  @Column()
  degreeName: string;

  @Column({ type: 'date' })
  startDate: Date;

  @Column({ type: 'date' })
  endDate: Date;

  @Exclude()
  @ManyToOne(() => Doctor, (doctor) => doctor.qualifications, {
    onDelete: 'CASCADE',
  })
  doctor: Doctor;

  @OneToOne(() => File, { nullable: false })
  @JoinColumn()
  degreeFile: File;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
