// src/doctor/entities/doctor.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { Qualification } from './qualification.entity';
import { PracticeExperience } from './practisceExperience.entity';
import { Award } from './award.entity';
import { File } from 'src/file/entities/file.entity';
import { Exclude } from 'class-transformer';
import { Consultation } from 'src/consultation/entities/consultation.entity';
import { WeeklySchedule } from 'src/schedule/entities/weeklySchedule.entity';
import { OverrideSchedule } from 'src/schedule/entities/overrideSchedule.entity';
import { Appointment } from 'src/appointment/entities/appointment.entity';
import { Emr } from 'src/emr/entities/emr.entity';

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
}

@Entity()
export class Doctor {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column()
  firstName: string;

  @Column()
  lastName: string;

  @Exclude()
  @Column()
  password: string;

  @Column({ nullable: true })
  contactNumber: string;

  @Column({ type: 'enum', enum: Gender, nullable: true })
  gender: Gender;

  @Column({ nullable: true, type: 'date' })
  dob: Date;

  @Column({ nullable: true })
  address: string;

  @Column('text', { array: true, nullable: true })
  language: string[];

  @Column({ nullable: true })
  country: string;

  @Column({ nullable: true })
  bio: string;

  //   WILL COME FROM ANOTHER TABLE
  @Column('text', { array: true, nullable: true })
  speciality: string[];

  @Column({ nullable: true })
  yearsOfExperience: number;

  @Column({ nullable: true })
  medicalLicenseNumber: string;

  @Column({ nullable: true })
  membershipDetails: string;

  @Column({ default: false })
  isProfileComplete: boolean;

  @Column({ default: false })
  isProfileVerfied: boolean;

  @Exclude()
  @Column({ nullable: true })
  otp: string;

  @Exclude()
  @Column({ nullable: true })
  otpExpires: Date;

  @Exclude()
  @Column({ nullable: true })
  resetPasswordToken: string;

  @OneToMany(() => Qualification, (qualification) => qualification.doctor, {
    cascade: true,
  })
  qualifications: Qualification[];

  @OneToMany(
    () => PracticeExperience,
    (practiceExperience) => practiceExperience.doctor,
    {
      cascade: true,
    },
  )
  practiceExperiences: PracticeExperience[];

  @OneToMany(() => Award, (award) => award.doctor, {
    cascade: true,
  })
  awards: Award[];

  @OneToMany(() => Consultation, (consultation) => consultation.doctor)
  consultations: Consultation[];

  @OneToMany(() => WeeklySchedule, (schedule) => schedule.doctor)
  weeklySchedules: WeeklySchedule[];

  @OneToMany(() => OverrideSchedule, (override) => override.doctor)
  overrideSchedule: OverrideSchedule[];

  @OneToMany(() => Appointment, (appointment) => appointment.doctor)
  appointments: Appointment[];

  @OneToMany(() => Emr, (emr) => emr.sharedBy)
  emrsSharedByMe: Emr[];

  @OneToMany(() => Emr, (emr) => emr.sharedTo)
  emrsSharedWithMe: Emr[];

  @OneToOne(() => File, { nullable: true })
  @JoinColumn()
  profilePicture: File;

  @OneToOne(() => File, { nullable: true })
  @JoinColumn()
  signature: File;

  @OneToOne(() => File, { nullable: true })
  @JoinColumn()
  idFront: File;

  @OneToOne(() => File, { nullable: true })
  @JoinColumn()
  idBack: File;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
