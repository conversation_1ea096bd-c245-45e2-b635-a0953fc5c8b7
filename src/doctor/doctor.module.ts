// src/doctor/doctor.module.ts
import { Module, forwardRef } from '@nestjs/common';
import { DoctorService } from './doctor.service';
import { Doctor<PERSON>ontroller } from './doctor.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Doctor } from './entities/doctor.entity';
import { Qualification } from './entities/qualification.entity';
import { PracticeExperience } from './entities/practisceExperience.entity';
import { Award } from './entities/award.entity';
import { AuthModule } from 'src/auth/auth.module';
import { FileModule } from 'src/file/file.module';
import { ScheduleModule } from 'src/schedule/schedule.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Doctor,
      Qualification,
      PracticeExperience,
      Award,
    ]),
    forwardRef(() => AuthModule),
    forwardRef(() => FileModule),
    forwardRef(() => ScheduleModule),
  ],
  providers: [DoctorService],
  controllers: [Doctor<PERSON>ontroller],
  exports: [DoctorService],
})
export class DoctorModule {}
