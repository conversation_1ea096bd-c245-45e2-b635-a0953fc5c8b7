// src/doctor/doctor.service.ts
import {
  BadRequestException,
  Inject,
  Injectable,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ILike, In, Raw, Repository } from 'typeorm';
import { Doctor } from './entities/doctor.entity';
import { CreateDoctorDto } from './dtos/create-doctor.dto';
import * as bcrypt from 'bcrypt';
import { UpdateDoctorDto } from './dtos/update-doctor.dto';
import { ForgotPasswordDoctorDto } from './dtos/forgotPassword-doctor.dto';
import { sendEmail, EmailOptions } from 'src/helpers/sendGrid.helper';
import { ResetPasswordDoctorDto } from './dtos/resetPassword-doctor.dto';
import { VerifyForgotPasswordOtpDoctorDto } from './dtos/verifyForgotPasswordOtp-doctor.dto';
import { randomBytes } from 'crypto';
import { File as MulterFile } from 'multer';
import { FileService } from 'src/file/file.service';
import { Qualification } from './entities/qualification.entity';
import { Award } from './entities/award.entity';
import { PracticeExperience } from './entities/practisceExperience.entity';
import { v4 as uuidv4 } from 'uuid';
import { File } from 'src/file/entities/file.entity';
import { DoctorWithUserType } from 'src/common/interfaces/doctorWithUserType.interface';
import { PatientWithUserType } from 'src/common/interfaces/patientWithUserType.interface';
import { FetchDoctorsDto } from './dtos/fetch-doctors.dto';
import moment from 'moment';
import { ScheduleService } from 'src/schedule/schedule.service';
import { UserType } from 'src/common/enums/userType.enum';
import { ChangePasswordDoctorDto } from './dtos/changePassword-doctor.dto';

@Injectable()
export class DoctorService {
  // CONSTRUCTOR
  constructor(
    @InjectRepository(Doctor)
    private readonly doctorRepository: Repository<Doctor>,

    @Inject(forwardRef(() => FileService))
    private readonly fileService: FileService,

    @Inject(forwardRef(() => ScheduleService))
    private readonly scheduleService: ScheduleService,
  ) {}

  // SERVICES
  async createDoctor(createDoctorDto: CreateDoctorDto): Promise<Doctor> {
    const { email, password, firstName, lastName } = createDoctorDto;

    // CHECK EXSISTING DOCTOR
    const exisitngDoctor = await this.doctorRepository.findOne({
      where: { email },
    });

    if (exisitngDoctor) {
      throw new BadRequestException('A doctor with this email already exists');
    }

    // PASSWORD HASH
    const hashedPassword = await bcrypt.hash(password, 10);

    // CREATE AND SAVE DOCTOR
    const doctor = this.doctorRepository.create({
      email,
      password: hashedPassword,
      firstName,
      lastName,
    });

    return await this.doctorRepository.save(doctor);
  }
  async updateDoctor(
    updateDoctorDto: UpdateDoctorDto,
    files: MulterFile[],
    user: DoctorWithUserType,
  ): Promise<Doctor | undefined> {
    const { doctorId } = updateDoctorDto;

    // SELF CHECK
    const doctorIdToUpdate = doctorId ? doctorId : user.id;
    if (doctorId && doctorId !== user.id) {
      throw new BadRequestException("You cannot update other's data");
    }

    // CHECK TO MAINTAIN UNIQUE EMAILS
    if (updateDoctorDto.email) {
      const existngDoctor = await this.doctorRepository.findOne({
        where: { email: updateDoctorDto.email },
      });
      if (existngDoctor && existngDoctor.id !== doctorIdToUpdate) {
        throw new BadRequestException('Email already in use');
      }
    }

    // CHECK DOCTOR
    const doctor = await this.doctorRepository.findOne({
      where: { id: doctorIdToUpdate },
      relations: [
        'qualifications',
        'qualifications.degreeFile',
        'awards',
        'practiceExperiences',
        'practiceExperiences.experienceFile',
        'profilePicture',
        'signature',
        'idFront',
        'idBack',
        'consultations',
      ],
    });
    if (!doctor) {
      throw new BadRequestException('Doctor not found');
    }

    // BASIC UPDATE
    const basicUpdatedData = {
      ...updateDoctorDto,
    };

    delete basicUpdatedData.doctorId;
    delete basicUpdatedData.profilePictureUrl;
    delete basicUpdatedData.signatureUrl;
    delete basicUpdatedData.idBackUrl;
    delete basicUpdatedData.idFrontUrl;
    delete basicUpdatedData.awards;
    delete basicUpdatedData.practiceExperiences;
    delete basicUpdatedData.qualifications;

    Object.assign(doctor, basicUpdatedData);

    // TOP LEVEL FILE UPLOADS

    // PROFILE PICTURE
    const profilePictureFile = files.find(
      (file) => file.fieldname === 'profilePicture',
    );
    if (profilePictureFile) {
      const uploadedProfilePicture = await this.fileService.uploadFile(
        profilePictureFile,
        'Doctor',
        doctor.id,
      );

      doctor.profilePicture = uploadedProfilePicture;
    } else if (updateDoctorDto.profilePictureUrl) {
      doctor.profilePicture = await this.fileService.createFileFromUrl(
        updateDoctorDto.profilePictureUrl,
        'Doctor',
        doctor.id,
      );
    }

    // SIGNATURE
    const signatureFile = files.find((file) => file.fieldname === 'signature');
    if (signatureFile) {
      const uploadedSignature = await this.fileService.uploadFile(
        signatureFile,
        'Doctor',
        doctor.id,
      );

      doctor.signature = uploadedSignature;
    } else if (updateDoctorDto.signatureUrl) {
      doctor.signature = await this.fileService.createFileFromUrl(
        updateDoctorDto.signatureUrl,
        'Doctor',
        doctor.id,
      );
    }

    // ID FRONT
    const idFrontFile = files.find((file) => file.fieldname === 'idFront');
    if (idFrontFile) {
      const uploadedIdFront = await this.fileService.uploadFile(
        idFrontFile,
        'Doctor',
        doctor.id,
      );
      doctor.idFront = uploadedIdFront;
    } else if (updateDoctorDto.idFrontUrl) {
      doctor.idFront = await this.fileService.createFileFromUrl(
        updateDoctorDto.idFrontUrl,
        'Doctor',
        doctor.id,
      );
    }

    // ID BACK
    const idBackFile = files.find((file) => file.fieldname === 'idBack');
    if (idBackFile) {
      const uploadedIdBack = await this.fileService.uploadFile(
        idBackFile,
        'Doctor',
        doctor.id,
      );
      doctor.idBack = uploadedIdBack;
    } else if (updateDoctorDto.idBackUrl) {
      doctor.idBack = await this.fileService.createFileFromUrl(
        updateDoctorDto.idBackUrl,
        'Doctor',
        doctor.id,
      );
    }

    // QUALIFICATIONS
    if (updateDoctorDto.qualifications) {
      const updatedQualifications: Qualification[] = [];

      // LOOP QUALIFICATIONS ARRAY
      for (let i = 0; i < updateDoctorDto.qualifications.length; i++) {
        const qualDto = updateDoctorDto.qualifications[i];
        let qualification: Qualification;

        // FIND EXISITING QUALIFICATION
        if (qualDto.id) {
          qualification = doctor.qualifications.find(
            (q) => q.id === qualDto.id,
          );

          if (!qualification) {
            // CREATE NEW IN CASE NONE FOUND
            qualification = new Qualification();
            qualification.id = uuidv4();
          }
        } else {
          // CREATE NEW IN CASE NO ID WAS SENT
          qualification = new Qualification();
          qualification.id = uuidv4();
        }

        // UPDATE QUALIFICATION FIELDS
        qualification.institutionName = qualDto.institutionName;
        qualification.degreeName = qualDto.degreeName;
        qualification.startDate = qualDto.startDate;
        qualification.endDate = qualDto.endDate;

        // FILE UPLOAD
        // FILE NAMING CONVENTION TO BE FOLLOWED : qualification_<index>_degreeFile
        const fileFieldName = `qualification_${i}_degreeFile`;

        const matchingQualificatonFile = files.find(
          (file) => file.fieldname === fileFieldName,
        );

        if (matchingQualificatonFile) {
          const uploadedDegreeFile = await this.fileService.uploadFile(
            matchingQualificatonFile,
            'Qualification',
            qualification.id,
          );

          qualification.degreeFile = uploadedDegreeFile;
        } else if (qualDto.degreeFileUrl) {
          const uploadedDegreeFile = await this.fileService.createFileFromUrl(
            qualDto.degreeFileUrl,
            'Qualification',
            qualification.id,
          );

          qualification.degreeFile = uploadedDegreeFile;
        } else {
          // IF USER WAS CREATING NEW QUALIFICATION THEN FILE IS NEEDED
          if (!qualDto.id) {
            throw new BadRequestException(
              'Degree File is needed for adding qualification',
            );
          }
        }

        // ADD DOCTOR RELATION
        qualification.doctor = doctor;

        updatedQualifications.push(qualification);
      }

      // ADD TO DOCTOR RECORD
      doctor.qualifications = updatedQualifications;
    }

    // AWARDS
    if (updateDoctorDto.awards) {
      const updatedAwards: Award[] = [];

      // LOOP AWARDS ARRAY
      for (let i = 0; i < updateDoctorDto.awards.length; i++) {
        const awardDto = updateDoctorDto.awards[i];
        let award: Award;

        // FIND EXISITING AWARDS
        if (awardDto.id) {
          award = doctor.awards.find((a) => a.id === awardDto.id);

          if (!award) {
            // CREATE NEW IN CASE NONE FOUND
            award = new Award();
            award.id = uuidv4();
          }
        } else {
          // CREATE NEW IN CASE NO ID WAS SENT
          award = new Award();
          award.id = uuidv4();
        }
        // UPDATE AWARD FIELDS
        award.awardName = awardDto.awardName;
        award.awardDetails = awardDto.awardDetails;
        award.issueDate = awardDto.issueDate;
        award.awardedBy = awardDto.awardedBy;

        // ADD DOCTOR RELATION
        award.doctor = doctor;

        updatedAwards.push(award);
      }

      // ADD TO DOCTOR RECORD
      doctor.awards = updatedAwards;
    }

    // PRACTICE EXPERIENCES
    if (updateDoctorDto.practiceExperiences) {
      const updatedPracticeExperiences: PracticeExperience[] = [];

      // LOOP PRACTICE-EXP ARRAY
      for (let i = 0; i < updateDoctorDto.practiceExperiences.length; i++) {
        const practiceExpDto = updateDoctorDto.practiceExperiences[i];
        let practiceExp: PracticeExperience;

        // FIND EXISITING PRACTICE-EXP
        if (practiceExpDto.id) {
          practiceExp = doctor.practiceExperiences.find(
            (p) => p.id === practiceExpDto.id,
          );

          if (!practiceExp) {
            // CREATE NEW IN CASE NONE FOUND
            practiceExp = new PracticeExperience();
            practiceExp.id = uuidv4();
          }
        } else {
          // CREATE NEW IN CASE NO ID WAS SENT
          practiceExp = new PracticeExperience();
          practiceExp.id = uuidv4();
        }
        // UPDATE PRACTICE-EXP FIELDS
        practiceExp.designation = practiceExpDto.designation;
        practiceExp.description = practiceExpDto.description;
        practiceExp.hospitalName = practiceExpDto.hospitalName;
        practiceExp.endDate = practiceExpDto.endDate;
        practiceExp.startDate = practiceExpDto.startDate;

        // FILE UPLOAD
        // FILE NAMING CONVENTION TO BE FOLLOWED : practice<index>experienceFile
        const fileFieldName = `practice_${i}_experienceFile`;

        const matchingPracticeExpFile = files.find(
          (file) => file.fieldname === fileFieldName,
        );

        if (matchingPracticeExpFile) {
          const uploadedPracticeExpFile = await this.fileService.uploadFile(
            matchingPracticeExpFile,
            'PracticeExperience',
            practiceExp.id,
          );

          practiceExp.experienceFile = uploadedPracticeExpFile;
        } else if (practiceExpDto.experienceFileUrl) {
          const uploadedPracticeExpFile =
            await this.fileService.createFileFromUrl(
              practiceExpDto.experienceFileUrl,
              'PracticeExperience',
              practiceExp.id,
            );

          practiceExp.experienceFile = uploadedPracticeExpFile;
        } else {
          // IF USER WAS CREATING NEW AWARD THEN FILE IS NEEDED
          if (!practiceExp.id) {
            throw new BadRequestException(
              'Practice-Experience file is needed for adding Practice-Experiance',
            );
          }
        }

        // ADD DOCTOR RELATION
        practiceExp.doctor = doctor;

        updatedPracticeExperiences.push(practiceExp);
      }

      // ADD TO DOCTOR RECORD
      doctor.practiceExperiences = updatedPracticeExperiences;
    }

    const savedDoctor = await this.doctorRepository.save(doctor);

    return savedDoctor;
  }
  async requestForgotDoctorPassword(
    forgotPasswordDoctorDto: ForgotPasswordDoctorDto,
  ): Promise<void> {
    const doctor = await this.doctorRepository.findOne({
      where: { email: forgotPasswordDoctorDto.email },
    });

    if (!doctor) {
      throw new BadRequestException('No doctor found against this email');
    }

    // GENERATE RESET OTP
    const randomNumber = Math.floor(Math.random() * 10000);
    const otp = randomNumber.toString().padStart(4, '0');

    doctor.otp = otp;
    doctor.otpExpires = new Date(Date.now() + 3600000); // 1 hour from now

    console.log('OTP => ', otp);

    const emailOptions: EmailOptions = {
      to: doctor.email,
      subject: 'Reset Password',
      text: `Your OTP for resetting password is ${otp}`,
      html: `<strong>Your OTP for resetting password is ${otp}</strong>`,
    };

    await this.doctorRepository.save(doctor);
    return;
  }
  async verifyForgotPasswordOtp(
    verifyForgotPasswordOtpDoctorDto: VerifyForgotPasswordOtpDoctorDto,
  ): Promise<string> {
    // CHECK DOCTOR
    const doctor = await this.doctorRepository.findOne({
      where: { email: verifyForgotPasswordOtpDoctorDto.email },
    });

    if (!doctor) {
      throw new BadRequestException('No doctor found against this email');
    }

    // CHECK OTP
    if (!doctor.otp || doctor.otp !== verifyForgotPasswordOtpDoctorDto.otp) {
      throw new BadRequestException('Invalid OTP');
    }

    // CHECK EXPIRY
    const now = new Date();
    if (!doctor.otpExpires || doctor.otpExpires < now) {
      throw new BadRequestException('OTP has expired');
    }

    // CLEAR OTP
    doctor.otp = null;
    doctor.otpExpires = null;

    // GENREATE RESET PASSWORD TOKEN
    doctor.resetPasswordToken = randomBytes(32).toString('hex');
    // SAVE PATIENT
    await this.doctorRepository.save(doctor);

    return doctor.resetPasswordToken;
  }
  async resetDoctorPassword(
    resetPasswordDoctorDto: ResetPasswordDoctorDto,
  ): Promise<void> {
    // CHECK DOCTOR
    const doctor = await this.doctorRepository.findOne({
      where: { email: resetPasswordDoctorDto.email },
    });

    if (!doctor) {
      throw new BadRequestException('No doctor found against this email');
    }

    // CHECK RESET PASSWORD TOKEN
    if (
      !doctor.resetPasswordToken ||
      doctor.resetPasswordToken !== resetPasswordDoctorDto.resetPasswordToken
    ) {
      throw new BadRequestException('Invalid token');
    }

    // HASH PASSWORD
    const hashedPassword = await bcrypt.hash(
      resetPasswordDoctorDto.newPassword,
      10,
    );
    doctor.password = hashedPassword;

    // CLEAR RESET PASSWORD TOKEN
    doctor.resetPasswordToken = null;

    // SAVE DOCTOR
    await this.doctorRepository.save(doctor);
    return;
  }
  async fetchDoctors(
    user: DoctorWithUserType | PatientWithUserType,
    fetchDoctorsDto: FetchDoctorsDto,
  ): Promise<{
    doctors: Doctor[];
    total: number;
    offset: number;
    limit: number;
  }> {
    const {
      speciality,
      date,
      country,
      language,
      offset = 0,
      limit = 20,
    } = fetchDoctorsDto;

    const whereConditions: any = {
      isProfileVerfied: true,
    };

    if (speciality?.length) {
      whereConditions.speciality = Raw(
        (alias) =>
          `${alias} && ARRAY[${speciality.map((s) => `'${s}'`).join(',')}]::text[]`,
      );
    }

    if (country?.length) {
      whereConditions.country = Raw(
        (alias) => `${alias} IN (${country.map((c) => `'${c}'`).join(',')})`,
      );
    }

    if (language?.length) {
      whereConditions.language = Raw(
        (alias) =>
          `${alias} && ARRAY[${language.map((l) => `'${l}'`).join(',')}]::text[]`,
      );
    }

    if (!date) {
      const [doctors, total] = await this.doctorRepository.findAndCount({
        where: whereConditions,
        skip: offset,
        take: limit,
        relations: ['profilePicture', 'qualifications'],
      });

      return {
        doctors,
        total,
        offset,
        limit,
      };
    }

    const doctorsAvailableOnDate =
      await this.scheduleService.getDoctorsAccordingToDate(
        new Date(date).toISOString().split('T')[0],
      );

    const availableDoctorIds = doctorsAvailableOnDate.map((doc) => doc.id);

    if (!availableDoctorIds.length) {
      return {
        doctors: [],
        total: 0,
        offset,
        limit,
      };
    }

    const [doctors, total] = await this.doctorRepository.findAndCount({
      where: {
        ...whereConditions,
        id: In(availableDoctorIds),
      },
      skip: offset,
      take: limit,
      relations: ['profilePicture', 'qualifications'],
    });

    return {
      doctors,
      total,
      offset,
      limit,
    };
  }
  async fetchDoctorById(
    user: DoctorWithUserType | PatientWithUserType,
    doctorId: string,
  ): Promise<any> {
    // SELF CHECK
    let doctorIdToUse: string;
    if (!doctorId) {
      if (user.userType !== UserType.DOCTOR) {
        throw new BadRequestException('Please send doctorId');
      }
      doctorIdToUse = user.id;
    } else {
      doctorIdToUse = doctorId;
    }

    const doctorFound = await this.doctorRepository.findOne({
      where: { id: doctorId },
      relations: [
        'qualifications',
        'qualifications.degreeFile',
        'awards',
        'practiceExperiences',
        'practiceExperiences.experienceFile',
        'profilePicture',
        'signature',
        'consultations',
        'weeklySchedules',
        'weeklySchedules.sessions',
      ],
    });
    return doctorFound;
  }
  async changeDoctorPassword(
    changePasswordDoctorDto: ChangePasswordDoctorDto,
    user: DoctorWithUserType,
  ): Promise<void> {
    // CHECK DOCTOR
    const exisitngDoctor = await this.doctorRepository.findOne({
      where: { id: user.id },
    });

    if (!exisitngDoctor) {
      throw new BadRequestException('No doctor found against this id');
    }

    // VERIFY OLD PASSWORD
    const isPasswordCorrect = await bcrypt.compare(
      changePasswordDoctorDto.oldPassword,
      exisitngDoctor.password,
    );
    if (!isPasswordCorrect) {
      throw new BadRequestException('Wrong old password');
    }

    // CHECK FOR SAME PASSWORD
    const isPasswordSame = await bcrypt.compare(
      changePasswordDoctorDto.newPassword,
      exisitngDoctor.password,
    );
    if (isPasswordSame) {
      throw new BadRequestException(
        'New password shouldnt be same as old password',
      );
    }

    // HASH PASSWORD
    const hashedPassword = await bcrypt.hash(
      changePasswordDoctorDto.newPassword,
      10,
    );
    exisitngDoctor.password = hashedPassword;

    // SAVE DOCTOR
    await this.doctorRepository.save(exisitngDoctor);
    return;
  }

  // HELPER FUNCTIONS
  // FINDS DOCTOR BY EMAIL WITH ALL RELATIONS
  async findByEmail(email: string): Promise<Doctor | undefined> {
    return this.doctorRepository.findOne({
      where: { email },
      relations: [
        'qualifications',
        'qualifications.degreeFile',
        'awards',
        'practiceExperiences',
        'practiceExperiences.experienceFile',
        'profilePicture',
        'signature',
        'idFront',
        'idBack',
        'consultations',
      ],
    });
  }
  // FINDS DOCTOR BY ID WITH ALL RELATIONS
  async findById(id: string): Promise<Doctor | undefined> {
    return this.doctorRepository.findOne({
      where: { id },
      relations: [
        'qualifications',
        'qualifications.degreeFile',
        'awards',
        'practiceExperiences',
        'practiceExperiences.experienceFile',
        'profilePicture',
        'signature',
        'idFront',
        'idBack',
        'consultations',
      ],
    });
  }
}
