// src/doctor/dto/fetch-doctors.dto.ts

import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsDate,
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MinLength,
} from 'class-validator';

export class FetchDoctorsDto {
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  speciality: string[];

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  date: Date;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  country: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  language: string[];

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  offset?: number;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number;
}
