// src/doctor/dto/resetPassword-doctor.dto.ts

import { Transform } from 'class-transformer';
import { IsEmail, IsNotEmpty, IsString, MinLength } from 'class-validator';

export class ResetPasswordDoctorDto {
  @IsEmail()
  @Transform(({ value }) => value.toLowerCase())
  email: string;

  @IsString()
  @IsNotEmpty()
  resetPasswordToken: string;

  @IsString()
  @MinLength(6)
  newPassword: string;
}
