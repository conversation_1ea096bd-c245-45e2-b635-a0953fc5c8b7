// src/database/database.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { DoctorSubscriber } from 'src/doctor/subscribers/doctor.subscriber';
import { ConsultationSubscriber } from 'src/consultation/subscribers/consultation.subscriber';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: `.env.${process.env.NODE_ENV || 'development'}`,
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DATABASE_HOST,
      port: parseInt(process.env.DATABASE_PORT || '5432', 10),
      username: process.env.DATABASE_USER,
      password: process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_NAME,
      subscribers: [DoctorSubscriber, ConsultationSubscriber],
      autoLoadEntities: true,
      synchronize: true, // FOR DEV ONLY. USE MIGRATIONS IN PROD
    }),
  ],
})
export class DatabaseModule {}
