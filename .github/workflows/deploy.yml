name: CI/CD Backend

on:
  push:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Deploy to EC2
        uses: appleboy/ssh-action@v0.1.7
        with:
          host: ${{ secrets.EC2_HOST }}
          username: ${{ secrets.EC2_USER }}
          key: ${{ secrets.EC2_SSH_KEY }}
          port: 22
          debug: true
          script: |
            cd /home/<USER>/docmobile_portal_backend_nest
            git remote set-<NAME_EMAIL>:BlackLion-Software/docmobile_portal_backend_nest.git
            git pull origin main
            npm install
            pm2 restart docmobile-portal
