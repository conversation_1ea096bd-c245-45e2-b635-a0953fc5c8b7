name: CI/CD Backend

on:
  push:
    branches: [development]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Deploy to EC2
        uses: appleboy/ssh-action@v0.1.7
        with:
          host: ${{ secrets.EC2_HOST_DEV }}
          username: ${{ secrets.EC2_USER_DEV }}
          key: ${{ secrets.EC2_SSH_KEY_DEV }}
          port: 22
          debug: true
          script: |
            cd /home/<USER>/docmobile_portal_backend_nest
            git remote set-<NAME_EMAIL>:BlackLion-Software/docmobile_portal_backend_nest.git
            git pull origin development
            npm install
            pm2 restart docmobile-portal-dev
